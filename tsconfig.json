{"compilerOptions": {"target": "ES2022", "lib": ["DOM", "DOM.Iterable", "ES2022"], "module": "esnext", "moduleResolution": "bundler", "esModuleInterop": true, "strict": false, "strictNullChecks": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "composite": true, "declaration": true, "sourceMap": true, "noEmit": false}, "exclude": ["**/node_modules", "**/dist", "**/.next"]}