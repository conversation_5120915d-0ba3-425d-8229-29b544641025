# Using Next.js Image with Static Asset CDN

This document explains how to use Next.js Image component with our Static Asset CDN for optimized image delivery.

## Benefits of Next.js Image

The Next.js Image component (`next/image`) provides several advantages over standard HTML `<img>` tags:

1. **Automatic Image Optimization**:
   - Resizes images to avoid shipping large images to devices with smaller viewports
   - Converts images to modern formats like WebP and AVIF when the browser supports them
   - Lazy loads images by default (loads as they enter the viewport)

2. **Visual Stability**:
   - Prevents layout shifts automatically by requiring width and height or aspect ratio
   - Maintains the correct aspect ratio

3. **Asset Flexibility**:
   - Works with both local and remote images
   - Integrates with CDNs and object storage services

## Components Available

Our implementation provides several components for different use cases:

### 1. StaticAsset (Recommended)

Uses Next.js Image component with our CDN configuration:

```tsx
import { StaticAsset } from '@/utilities/static-asset-cdn'

// Basic usage with required width and height
<StaticAsset
  src="/demo-images/logo.png"
  alt="Company Logo"
  width={200}
  height={100}
/>

// With additional Next.js Image props
<StaticAsset
  src="/demo-images/hero.jpg"
  alt="Hero Image"
  width={1200}
  height={600}
  priority
  quality={90}
  sizes="(max-width: 768px) 100vw, 1200px"
/>
```

### 2. StaticImg (Legacy)

Uses standard HTML `<img>` tag with our CDN configuration. Only use this when Next.js Image doesn't meet your needs:

```tsx
import { StaticImg } from '@/utilities/static-asset-cdn'

<StaticImg
  src="/demo-images/logo.png"
  alt="Company Logo"
  className="w-48 h-auto"
/>
```

### 3. Video Components

For video assets, we provide several components:

```tsx
import { 
  StaticVideo, 
  StaticVideoWithSources,
  BackgroundVideo 
} from '@/utilities/static-asset-cdn'

// Basic video
<StaticVideo
  src="/videos/product-demo.mp4"
  controls
  poster="/images/video-poster.jpg"
/>

// Video with multiple sources
<StaticVideoWithSources
  poster="/images/video-poster.jpg"
  controls
  sources={[
    { src: "/videos/product-demo.mp4", type: "video/mp4" },
    { src: "/videos/product-demo.webm", type: "video/webm" }
  ]}
/>

// Background video (autoplay, loop, muted, playsInline)
<BackgroundVideo
  src="/videos/background.mp4"
  className="absolute inset-0 w-full h-full object-cover"
/>
```

## Required Props for Next.js Image

When using the `StaticAsset` component (which uses Next.js Image), you must provide:

1. **src**: Path to the image (required)
2. **alt**: Descriptive text for screen readers (required)
3. **width** and **height**: Dimensions of the image (required unless using `fill`)

## Optional Props

All Next.js Image props are supported, including:

- **quality**: 1-100, default is 75
- **priority**: Boolean, preload the image
- **placeholder**: 'blur' or 'empty'
- **sizes**: Responsive size information
- **fill**: Boolean, make the image fill its parent container

## Environment Configuration

For the CDN functionality to work in production, set these environment variables:

```
NEXT_PUBLIC_STATIC_ASSET_PREFIX=https://your-cdn-domain.com/bucket-name
```

## Best Practices

1. **Always specify width and height**:
   - This prevents layout shifts
   - Use the original image dimensions or maintain the same aspect ratio

2. **Use the `sizes` attribute for responsive images**:
   - This helps the browser select the right image size
   - Example: `sizes="(max-width: 768px) 100vw, 50vw"`

3. **Use `priority` for above-the-fold images**:
   - This preloads important images
   - Use sparingly for critical content like hero images

4. **Optimize your source images**:
   - Even though Next.js optimizes images, starting with optimized sources is better
   - Use appropriate formats (JPEG for photos, PNG for graphics with transparency)

5. **Consider using `fill` for responsive layouts**:
   - When the image needs to fill its container
   - Combine with object-fit CSS property

## Troubleshooting

### Images Not Loading in Production

1. Check that `NEXT_PUBLIC_STATIC_ASSET_PREFIX` is correctly set
2. Verify that the assets have been uploaded to the object store
3. Ensure the bucket has public access enabled
4. Check browser console for CORS errors

### Next.js Image Warnings

1. **Missing `alt` prop**: Always provide descriptive alt text
2. **Missing `width` and `height`**: Required unless using `fill`
3. **Large images**: Consider using a smaller source image

### Performance Issues

1. Use the Network tab in browser DevTools to check image loading times
2. Consider using the `quality` prop to reduce file size
3. Use appropriate `sizes` values for responsive images
