# StitchEstimate Embroidery Pattern Library Documentation

Welcome to the documentation for the StitchEstimate embroidery pattern library. This documentation provides comprehensive information about the pattern library, stitch types, and how to work with embroidery files.

## Documentation Files

- [Embroidery Stitch Types](embroidery-stitch-types.md) - Comprehensive explanation of stitch types and their rendering behavior
- [Embroidery Stitch Diagrams](embroidery-stitch-diagrams.md) - Visual diagrams illustrating stitch types and rendering
- [Embroidery Stitch Code Examples](embroidery-stitch-code-examples.md) - Code examples for working with different stitch types

## Quick Reference

### Stitch Types

The pattern library defines five primary stitch types:

| Stitch Type | Value | Description |
|-------------|-------|-------------|
| Normal      | 0     | Regular stitch that forms the embroidery design |
| Jump        | 1     | Movement without stitching (thread above fabric) |
| Trim        | 2     | Indicates thread should be cut |
| Stop        | 4     | Indicates a color change |
| End         | 8     | Marks the end of the pattern |

### Line Segment Connections

- **Normal → Normal**: Connected with a line segment
- **Normal → Jump/Trim/Stop**: No line segment (path ends)
- **Jump/Trim/Stop → Normal**: New path starts (no connection to previous path)

### Common Issues

1. **Missing Line Segments**: Check if stitches are incorrectly marked as jump/trim
2. **Unwanted Line Segments**: Check if jumps/trims are incorrectly marked as normal
3. **First Stitch Connected Incorrectly**: Mark first stitch as jump if needed
4. **Inverted Pattern**: Invert Y coordinates or use `pattern.invertPatternVertical()`

## Key Files in the Codebase

- `src/utilities/stitch-file-processing/pattern.ts` - Defines the Pattern class and stitch types
- `src/components/embroidery/FabricEmbroideryViewerUtils.ts` - Handles rendering of embroidery patterns
- `src/utilities/stitch-file-processing/formats/*.ts` - Format-specific parsers

## Format-Specific Considerations

Different embroidery file formats handle stitch types in different ways:

- **EXP Format**: Uses special control codes (0x80) to indicate special stitches
- **JEF Format**: Uses specific byte sequences to indicate stitch types
- **VP3 Format**: Has a more complex structure with explicit color sections

For more detailed information, please refer to the specific documentation files.
