# Testing Documentation

This document outlines how to run and manage tests in the project.

## Prerequisites

Before running tests, ensure you have:
1. Node.js installed
2. PNPM package manager installed
3. Project dependencies installed via `pnpm install`

## Unit Tests

Unit tests are located in `__tests__` directories throughout the codebase.

### Running All Unit Tests

```bash
pnpm test
```

### Running Specific Unit Tests

To run tests for a specific file or directory:

```bash
# Run tests in a specific file
pnpm test src/components/calculator/canvas/__tests__/debug.test.ts

# Run tests matching a pattern
pnpm test calculator
```

### Watch Mode

To run tests in watch mode (tests re-run when files change):

```bash
pnpm test:watch
```

## End-to-End (E2E) Tests

E2E tests are located in the `e2e` directory and use Playwright for browser automation.

### Installing Playwright Browsers

Before running E2E tests for the first time:

```bash
pnpm playwright install
```

### Running All E2E Tests

```bash
pnpm test:e2e
```

### Headed vs Headless Mode

By default, E2E tests run in headless mode (no visible browser). To run tests with a visible browser:

```bash
# Run all tests in headed mode
pnpm playwright test --headed

# Run specific test file in headed mode
pnpm playwright test e2e/calculator/zoom-and-pan.spec.ts --headed

# Force headless mode (default)
pnpm playwright test --headless
```

### Running Headed Tests on Remote Systems

When developing on a remote system (e.g., via SSH), you'll need X forwarding to run headed tests. Here's how to set it up:

1. Enable X forwarding either through SSH command or config:

   Option 1 - SSH command:
   ```bash
   ssh -X user@remote-host
   # or
   ssh -Y user@remote-host  # More permissive forwarding
   ```

   Option 2 - SSH config file (~/.ssh/config):
   ```bash
   Host remote-host
       HostName remote-host
       User user
       ForwardX11 yes
       # Or for trusted X11 forwarding (equivalent to -Y):
       ForwardX11Trusted yes
   ```
   Then simply connect with: `ssh remote-host`

2. Set the display environment variable if not automatically set:
```bash
export DISPLAY=:0
```

3. Run headed tests as normal:
```bash
pnpm playwright test --headed
```

Note: If you experience performance issues with X forwarding, you can:
- Use compression in command: `ssh -XC user@remote-host`
- Or add compression in config: `Compression yes`
- Consider using headless mode for most testing
- Use the Playwright UI mode which is often more performant: `pnpm playwright test --ui`

### Running Specific E2E Tests

```bash
# Run a specific test file
pnpm playwright test e2e/calculator/zoom-and-pan.spec.ts

# Run tests in a specific directory
pnpm playwright test e2e/calculator/

# Run tests with a specific title
pnpm playwright test -g "background removal"
```

### Running E2E Tests in UI Mode

Playwright offers a UI mode for debugging and developing tests:

```bash
pnpm playwright test --ui
```

### Running E2E Tests in Different Browsers

By default, tests run in all configured browsers. To target a specific browser:

```bash
pnpm playwright test --project=chromium
pnpm playwright test --project=firefox
pnpm playwright test --project=webkit
```

## Test Reports

### Unit Test Coverage

To generate a coverage report for unit tests:

```bash
pnpm test:coverage
```

### E2E Test Report

After running E2E tests, view the HTML report:

```bash
pnpm playwright show-report
```

## Debugging Tests

### Unit Tests

Add the `debug` statement to your test:

```typescript
test('my test', () => {
  debug(); // Test will pause here
  // Your test code
});
```

Then run:

```bash
pnpm test:debug
```

### E2E Tests

1. Run tests in debug mode:
```bash
pnpm playwright test --debug
```

2. Or use UI mode for visual debugging:
```bash
pnpm playwright test --ui
```

## Continuous Integration

Tests are automatically run in CI/CD pipelines. The full test suite can be run with:

```bash
pnpm test:ci
```

This command runs both unit and E2E tests in a CI-friendly manner. 