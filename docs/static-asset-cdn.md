# Static Asset CDN for StitchEstimate

This document explains how to use the Static Asset CDN feature to serve static assets from an object store like Cloudflare R2 instead of directly from the Vercel deployment.

## Why Use a CDN for Static Assets?

1. **Reduced Bandwidth Costs**: Vercel charges for bandwidth usage. By serving static assets from a dedicated object store, you can significantly reduce these costs.
2. **Better Performance**: CDNs are optimized for serving static content with low latency globally.
3. **Scalability**: Object stores like Cloudflare R2 are designed to handle large volumes of static assets efficiently.

## Configuration

### Environment Variables

Add the following environment variables to your `.env` file:

```
# Object Storage (for PDF reports, media uploads, and static assets)
OBJECT_STORAGE_ACCESS_KEY=your_access_key
OBJECT_STORAGE_SECRET_KEY=your_secret_key
OBJECT_STORAGE_BUCKET=stitchestimate-bucket
OBJECT_STORAGE_REGION=auto
OBJECT_STORAGE_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
OBJECT_STORAGE_PATH_STYLE=true

# Static Asset CDN
NEXT_PUBLIC_STATIC_ASSET_PREFIX=https://pub-your-account-id.r2.dev/stitchestimate-bucket
```

### Cloudflare R2 Setup

1. Create a Cloudflare R2 bucket
2. Create an API token with read/write access to the bucket
3. Configure public access for the bucket
4. Set up the environment variables as shown above

## Usage

### In React Components

Use the provided utility components to reference static assets:

```tsx
import { StaticVideo, StaticAsset } from '@/utilities/static-asset-cdn'

// For videos
<StaticVideo
  src="/videos/embroidery-bg.mp4"
  autoPlay
  loop
  muted
  playsInline
  className="absolute inset-0 h-full w-full object-cover"
/>

// For images
<StaticAsset
  src="/demo-images/test-logo.png"
  alt="Test Logo"
  className="w-full h-auto"
/>
```

### Direct URL Access

If you need to get the URL for a static asset programmatically:

```tsx
import { getStaticAssetUrl } from '@/utilities/static-asset-cdn'

const logoUrl = getStaticAssetUrl('/demo-images/test-logo.png')
```

## Uploading Static Assets

### Automatic Upload During Build

To automatically upload static assets during the build process, add this to your build script:

```json
"build": "cross-env NODE_OPTIONS=--no-deprecation next build && pnpm upload:static"
```

### Manual Upload

Use the provided npm scripts to upload static assets:

```bash
# Upload all static assets
pnpm upload:static

# Upload only videos
pnpm upload:static:videos

# Upload only demo images
pnpm upload:static:demo-images
```

### Testing Uploads

To test the upload process without actually uploading files:

```bash
pnpm upload:static --dry-run
```

## Development vs Production

- In development, static assets are served directly from the `/public` directory
- In production, static assets are served from the configured object store
- The system automatically detects the environment and uses the appropriate method

## Migrating Existing References

When migrating existing components to use the CDN:

1. Import the appropriate component from `@/utilities/static-asset-cdn`
2. Replace direct references to static assets with the component
3. For video elements with `<source>` tags, use the `StaticVideo` component with the `src` prop

## Troubleshooting

### Assets Not Loading in Production

1. Check that `NEXT_PUBLIC_STATIC_ASSET_PREFIX` is correctly set
2. Verify that the assets have been uploaded to the object store
3. Ensure the bucket has public access enabled
4. Check browser console for CORS errors

### Assets Not Uploading

1. Verify your object storage credentials
2. Check that the bucket exists and is writable
3. Run the upload script with `--verbose` flag for detailed logs

## Best Practices

1. Keep static assets organized in the `/public` directory
2. Use descriptive filenames
3. Optimize images and videos before uploading
4. Use the CDN for all static assets, not just large files
5. Consider adding cache headers for better performance
