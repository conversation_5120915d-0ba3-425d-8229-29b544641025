# Embroidery Stitch Types Documentation

This document provides a comprehensive explanation of the different stitch types used in the StitchEstimate pattern library, how they are rendered, and when to use each type.

## Table of Contents

1. [Stitch Type Overview](#stitch-type-overview)
2. [Stitch Type Definitions](#stitch-type-definitions)
3. [Rendering Behavior](#rendering-behavior)
4. [Line Segment Connections](#line-segment-connections)
5. [Format-Specific Considerations](#format-specific-considerations)
6. [Debugging Stitch Rendering](#debugging-stitch-rendering)

## Stitch Type Overview

The pattern library defines five primary stitch types:

| Stitch Type | Value | Description |
|-------------|-------|-------------|
| Normal      | 0     | Regular stitch that forms the embroidery design |
| Jump        | 1     | Movement without stitching (thread above fabric) |
| Trim        | 2     | Indicates thread should be cut |
| Stop        | 4     | Indicates a color change |
| End         | 8     | Marks the end of the pattern |

## Stitch Type Definitions

### Normal Stitch (0)
- **Purpose**: Creates visible stitches on the fabric
- **Behavior**: Forms a continuous line of thread on the fabric
- **Visual Representation**: Connected line segments in the rendered design
- **When to Use**: For all standard embroidery stitches that should be visible

### Jump Stitch (1)
- **Purpose**: Moves the needle without creating a stitch
- **Behavior**: The machine raises the needle and moves to a new position without stitching
- **Visual Representation**: No line segment is drawn between the previous stitch and the jump stitch
- **When to Use**: When moving to a different area of the design without wanting a visible connection

### Trim Stitch (2)
- **Purpose**: Indicates where the thread should be cut
- **Behavior**: The machine cuts the thread and may move to a new position
- **Visual Representation**: No line segment is drawn between the previous stitch and the trim stitch
- **When to Use**: At the end of a section before moving to a disconnected section with the same thread color

### Stop Stitch (4)
- **Purpose**: Indicates a color change
- **Behavior**: The machine stops and waits for the thread to be changed
- **Visual Representation**: No line segment is drawn between the previous stitch and the stop stitch
- **When to Use**: When changing to a different thread color

### End Stitch (8)
- **Purpose**: Marks the end of the pattern
- **Behavior**: The machine stops and completes the embroidery process
- **Visual Representation**: Not rendered in the design
- **When to Use**: At the very end of the pattern

## Rendering Behavior

The rendering of stitches is handled by the `renderEmbroideryPatternPaths` function in `FabricEmbroideryViewerUtils.ts`. This function processes the pattern's stitches and creates paths for each color section.

### Path Generation Logic

1. **Normal Stitches**: Connected with line segments to form continuous paths
2. **Jump/Trim Stitches**: Break the current path and start a new path at the next normal stitch
3. **Stop Stitches (Color Changes)**: Break the current path and start a new path with a new color
4. **End Stitches**: Not included in the rendered paths

## Line Segment Connections

The following diagram illustrates how different stitch types affect line segment connections:

```
Normal → Normal: Connected with a line segment
   |
   v
Normal → Jump: No line segment (path ends)
   |
   v
Jump → Normal: New path starts (no connection to previous path)
   |
   v
Normal → Trim: No line segment (path ends)
   |
   v
Trim → Normal: New path starts (no connection to previous path)
   |
   v
Normal → Stop: No line segment (path ends)
   |
   v
Stop → Normal: New path starts with new color (no connection to previous path)
```

### Visual Example

```
Normal Stitches:  A---B---C---D
                  |   |   |   |
                  |   |   |   |
Jump Stitch:      E   F   G   H
                      |
                      |
Normal Stitches:      I---J---K
                          |
                          |
Trim Stitch:              L
                          |
                          |
Normal Stitches:          M---N---O
```

In this example:
- A, B, C, D are connected with line segments
- E, F, G, H are jump stitches, so no line segments connect them
- I, J, K are connected with line segments, but not connected to the previous section
- L is a trim stitch, so no line segment connects it to K
- M, N, O are connected with line segments, but not connected to L

## Format-Specific Considerations

Different embroidery file formats handle stitch types in different ways:

### EXP Format
- Uses special control codes (0x80) to indicate special stitches
- Color changes are indicated by 0x80, 0x01
- Trims are indicated by 0x80, 0x02
- Jumps are indicated by 0x80, 0x04
- Y-axis is inverted compared to other formats

### JEF Format
- Uses specific byte sequences to indicate stitch types
- May have extra stitches at the beginning that should be marked as jumps
- Stitching might start after the first trim command

### VP3 Format
- Has a more complex structure with explicit color sections
- Each color section starts with a jump stitch
- Contains detailed color information

## Debugging Stitch Rendering

When debugging stitch rendering issues, consider the following:

1. **Check Stitch Types**: Ensure stitches are correctly marked with the appropriate type
2. **Examine First Stitch**: The first stitch in a pattern or color section often needs special handling
3. **Look for Large Movements**: Unusually large movements might need to be marked as jumps
4. **Verify Y-Axis Direction**: Some formats invert the Y-axis compared to others
5. **Check for Zero-Movement Stitches**: Some formats use zero-movement stitches for special commands

### Common Issues and Solutions

| Issue | Possible Cause | Solution |
|-------|---------------|----------|
| Missing line segments | Stitches incorrectly marked as jump/trim | Mark normal stitches with `stitchTypes.normal` |
| Unwanted line segments | Normal stitches that should be jumps/trims | Mark large movements as `stitchTypes.jump` |
| First stitch connected incorrectly | First stitch not handled specially | Mark first stitch as `stitchTypes.jump` if needed |
| Pattern appears upside down | Y-axis not inverted for formats that need it | Use `pattern.invertPatternVertical()` or invert Y coordinates |
| Broken paths within same color | Trim/jump stitches breaking the path | Check for incorrect trim/jump flags |

## Conclusion

Understanding how different stitch types affect the rendering of embroidery patterns is crucial for correctly implementing format parsers. By properly marking stitches with the appropriate type flags, you can ensure that patterns are rendered correctly with the right connections between stitches.
