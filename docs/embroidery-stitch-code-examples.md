# Embroidery Stitch Type Code Examples

This document provides code examples for working with different stitch types in the StitchEstimate pattern library.

## Table of Contents

1. [Creating a Basic Pattern](#creating-a-basic-pattern)
2. [Working with Different Stitch Types](#working-with-different-stitch-types)
3. [Format Parser Implementation Examples](#format-parser-implementation-examples)
4. [Debugging Stitch Rendering](#debugging-stitch-rendering)

## Creating a Basic Pattern

Here's how to create a basic pattern with normal stitches:

```typescript
import { Pattern, stitchTypes } from '../utilities/stitch-file-processing/pattern'

// Create a new pattern
const pattern = new Pattern()

// Add a color
pattern.addColorRgb(0, 0, 0, 'Black')

// Add stitches to form a square
pattern.addStitchAbs(0, 0, stitchTypes.normal)
pattern.addStitchAbs(50, 0, stitchTypes.normal)
pattern.addStitchAbs(50, 50, stitchTypes.normal)
pattern.addStitchAbs(0, 50, stitchTypes.normal)
pattern.addStitchAbs(0, 0, stitchTypes.normal)

// Add end stitch
pattern.addStitchAbs(0, 0, stitchTypes.end)
```

## Working with Different Stitch Types

### Normal Stitches

Normal stitches are connected with line segments:

```typescript
// Add normal stitches
pattern.addStitchAbs(0, 0, stitchTypes.normal)
pattern.addStitchAbs(10, 0, stitchTypes.normal)
pattern.addStitchAbs(20, 0, stitchTypes.normal)
```

### Jump Stitches

Jump stitches create a break in the visible line segments:

```typescript
// Add normal stitches
pattern.addStitchAbs(0, 0, stitchTypes.normal)
pattern.addStitchAbs(10, 0, stitchTypes.normal)

// Add jump stitch
pattern.addStitchAbs(50, 0, stitchTypes.jump)

// Continue with normal stitches
pattern.addStitchAbs(60, 0, stitchTypes.normal)
pattern.addStitchAbs(70, 0, stitchTypes.normal)
```

### Trim Stitches

Trim stitches indicate where the thread should be cut:

```typescript
// Add normal stitches
pattern.addStitchAbs(0, 0, stitchTypes.normal)
pattern.addStitchAbs(10, 0, stitchTypes.normal)

// Add trim stitch
pattern.addStitchAbs(10, 0, stitchTypes.trim)

// Move to a new position and continue with normal stitches
pattern.addStitchAbs(50, 50, stitchTypes.normal)
pattern.addStitchAbs(60, 50, stitchTypes.normal)
```

### Stop Stitches (Color Changes)

Stop stitches indicate a color change:

```typescript
// Add normal stitches with first color
pattern.addStitchAbs(0, 0, stitchTypes.normal)
pattern.addStitchAbs(10, 0, stitchTypes.normal)

// Add stop stitch for color change
pattern.addStitchAbs(10, 0, stitchTypes.stop, true) // true enables auto color index increment

// Add a new color
pattern.addColorRgb(255, 0, 0, 'Red')

// Continue with normal stitches in the new color
pattern.addStitchAbs(20, 0, stitchTypes.normal)
pattern.addStitchAbs(30, 0, stitchTypes.normal)
```

### Combining Multiple Stitch Types

Here's an example of a pattern with multiple stitch types:

```typescript
// Create a new pattern
const pattern = new Pattern()

// Add colors
pattern.addColorRgb(0, 0, 0, 'Black')
pattern.addColorRgb(255, 0, 0, 'Red')
pattern.addColorRgb(0, 0, 255, 'Blue')

// First color section
pattern.addStitchAbs(0, 0, stitchTypes.normal)
pattern.addStitchAbs(10, 0, stitchTypes.normal)
pattern.addStitchAbs(20, 0, stitchTypes.normal)

// Jump to a new position
pattern.addStitchAbs(50, 0, stitchTypes.jump)
pattern.addStitchAbs(60, 0, stitchTypes.normal)
pattern.addStitchAbs(70, 0, stitchTypes.normal)

// Color change
pattern.addStitchAbs(70, 0, stitchTypes.stop, true)

// Second color section
pattern.addStitchAbs(70, 10, stitchTypes.normal)
pattern.addStitchAbs(70, 20, stitchTypes.normal)

// Trim and move to a new position
pattern.addStitchAbs(70, 20, stitchTypes.trim)
pattern.addStitchAbs(50, 50, stitchTypes.normal)
pattern.addStitchAbs(60, 50, stitchTypes.normal)

// Color change
pattern.addStitchAbs(60, 50, stitchTypes.stop, true)

// Third color section
pattern.addStitchAbs(60, 60, stitchTypes.normal)
pattern.addStitchAbs(60, 70, stitchTypes.normal)

// End the pattern
pattern.addStitchAbs(60, 70, stitchTypes.end)
```

## Format Parser Implementation Examples

### EXP Format Parser Example

Here's a simplified example of how to parse EXP format files:

```typescript
function parseExp(buffer: ArrayBuffer): Pattern {
  const pattern = new Pattern()
  pattern.addColorRgb(0, 0, 0, 'Black')
  
  const binary = new BinaryReader(buffer)
  let idx = 0
  
  while (idx < binary.view.byteLength) {
    // Read two bytes
    const b0 = binary.read('uint8') as number
    idx++
    
    if (idx >= binary.view.byteLength) break
    
    const b1 = binary.read('uint8') as number
    idx++
    
    // Handle special control codes
    if (b0 === 0x80) {
      if ((b1 & 1) !== 0) {
        // Color change (STOP)
        if (idx + 1 >= binary.view.byteLength) break
        
        const nextB0 = binary.read('uint8') as number
        idx++
        const nextB1 = binary.read('uint8') as number
        idx++
        
        // Add color change
        const colorIndex = pattern.colors.length
        pattern.addColorRgb(/* color values */)
        
        // Add stitch with stop flag
        const dx = expDecode(nextB0)
        const dy = expDecode(nextB1)
        pattern.addStitchRel(dx / 10.0, -dy / 10.0, stitchTypes.stop, true)
        continue
      } else if (b1 === 0x02 || b1 === 0x04) {
        // Trim or Jump
        if (idx + 1 >= binary.view.byteLength) break
        
        const nextB0 = binary.read('uint8') as number
        idx++
        const nextB1 = binary.read('uint8') as number
        idx++
        
        // Decode coordinates
        const dx = expDecode(nextB0)
        const dy = expDecode(nextB1)
        
        // Use jump for b1=0x04 and trim for b1=0x02
        const jumpOrTrimFlag = b1 === 0x04 ? stitchTypes.jump : stitchTypes.trim
        
        // Add the stitch with appropriate flag
        pattern.addStitchRel(dx / 10.0, -dy / 10.0, jumpOrTrimFlag, true)
        continue
      } else if (b1 === 0x80) {
        // Special case - trim with no movement
        if (idx + 1 >= binary.view.byteLength) break
        
        // Skip the next two bytes
        binary.read('uint8')
        idx++
        binary.read('uint8')
        idx++
        
        // Add trim with no movement
        pattern.addStitchRel(0, 0, stitchTypes.trim, true)
        continue
      }
    }
    
    // Normal stitch
    const dx = expDecode(b0)
    const dy = expDecode(b1)
    
    // Add normal stitch with inverted Y axis
    pattern.addStitchRel(dx / 10.0, -dy / 10.0, stitchTypes.normal, true)
  }
  
  // Add end stitch
  pattern.addStitchRel(0, 0, stitchTypes.end, true)
  
  return pattern
}

// Helper function to decode EXP bytes
function expDecode(input: number): number {
  return input > 128 ? -(~input & 255) - 1 : input
}
```

### JEF Format Parser Example

Here's a simplified example of how to parse JEF format files:

```typescript
function parseJef(buffer: ArrayBuffer): Pattern {
  const pattern = new Pattern()
  
  // Read header information
  // ...
  
  // Process stitch data
  let isFirstStitch = true
  
  while (/* more data */) {
    // Read stitch data
    const b0 = /* read byte */
    const b1 = /* read byte */
    
    // Check control bits
    if (b0 & 0x10) {
      // Color change
      pattern.addStitchRel(0, 0, stitchTypes.stop, true)
      // Add new color
      pattern.addColorRgb(/* color values */)
      isFirstStitch = true
    } else if (b0 & 0x20) {
      // Jump stitch
      const dx = /* calculate dx */
      const dy = /* calculate dy */
      pattern.addStitchRel(dx, dy, stitchTypes.jump, false)
    } else if (b0 & 0x40) {
      // End of pattern
      pattern.addStitchRel(0, 0, stitchTypes.end, false)
      break
    } else {
      // Normal stitch
      const dx = /* calculate dx */
      const dy = /* calculate dy */
      
      // If this is the first stitch after a color change, mark it as a jump
      if (isFirstStitch) {
        pattern.addStitchRel(dx, dy, stitchTypes.jump, false)
        isFirstStitch = false
      } else {
        pattern.addStitchRel(dx, dy, stitchTypes.normal, false)
      }
    }
  }
  
  return pattern
}
```

## Debugging Stitch Rendering

### Logging Stitch Information

When debugging stitch rendering issues, it's helpful to log stitch information:

```typescript
function logStitchInfo(pattern: Pattern): void {
  console.log(`Pattern has ${pattern.stitches.length} stitches and ${pattern.colors.length} colors`)
  
  for (let i = 0; i < pattern.stitches.length; i++) {
    const stitch = pattern.stitches[i]
    let typeStr = 'NORMAL'
    
    if (stitch.flags & stitchTypes.jump) typeStr = 'JUMP'
    if (stitch.flags & stitchTypes.trim) typeStr = 'TRIM'
    if (stitch.flags & stitchTypes.stop) typeStr = 'STOP'
    if (stitch.flags & stitchTypes.end) typeStr = 'END'
    
    console.log(`Stitch ${i}: x=${stitch.x}, y=${stitch.y}, type=${typeStr}, color=${stitch.color}`)
  }
}
```

### Visualizing Stitch Paths

To visualize how stitches will be connected, you can create a simple path visualization:

```typescript
function visualizeStitchPaths(pattern: Pattern): string {
  let result = ''
  let currentPath = ''
  let currentColorIndex = -1
  
  for (let i = 0; i < pattern.stitches.length; i++) {
    const stitch = pattern.stitches[i]
    
    if (stitch.flags & stitchTypes.end) {
      result += currentPath + ' (END)\n'
      break
    }
    
    if (currentColorIndex !== stitch.color) {
      if (currentPath) {
        result += currentPath + '\n'
      }
      currentPath = `Color ${stitch.color}: •`
      currentColorIndex = stitch.color
      continue
    }
    
    if (stitch.flags & stitchTypes.jump) {
      currentPath += '···•'
    } else if (stitch.flags & stitchTypes.trim) {
      currentPath += '✂···•'
    } else if (stitch.flags & stitchTypes.stop) {
      currentPath += '🎨'
      result += currentPath + '\n'
      currentPath = `Color ${stitch.color + 1}: •`
    } else {
      currentPath += '—•'
    }
  }
  
  return result
}
```

### Testing Stitch Type Handling

Here's an example of how to test if stitch types are being handled correctly:

```typescript
function testStitchTypeHandling(pattern: Pattern): void {
  // Count stitch types
  let normalCount = 0
  let jumpCount = 0
  let trimCount = 0
  let stopCount = 0
  let endCount = 0
  
  for (const stitch of pattern.stitches) {
    if (stitch.flags & stitchTypes.end) endCount++
    else if (stitch.flags & stitchTypes.stop) stopCount++
    else if (stitch.flags & stitchTypes.trim) trimCount++
    else if (stitch.flags & stitchTypes.jump) jumpCount++
    else normalCount++
  }
  
  console.log(`Stitch type counts:`)
  console.log(`- Normal: ${normalCount}`)
  console.log(`- Jump: ${jumpCount}`)
  console.log(`- Trim: ${trimCount}`)
  console.log(`- Stop: ${stopCount}`)
  console.log(`- End: ${endCount}`)
  
  // Check for common issues
  if (jumpCount === 0 && pattern.stitches.length > 100) {
    console.warn('No jump stitches found in a large pattern - this might indicate incorrect stitch type handling')
  }
  
  if (stopCount !== pattern.colors.length - 1) {
    console.warn(`Mismatch between stop stitches (${stopCount}) and colors (${pattern.colors.length})`)
  }
  
  if (endCount !== 1) {
    console.warn(`Pattern should have exactly 1 end stitch, but has ${endCount}`)
  }
}
```

These examples should help you understand how to work with different stitch types in the StitchEstimate pattern library.
