# Stitch Types Implementation Guide

This document provides a comprehensive explanation of how stitch types are implemented in the StitchEstimate pattern library, how they affect rendering, and how to properly handle them in format parsers.

## Table of Contents

1. [Stitch Type Definitions](#stitch-type-definitions)
2. [Rendering Behavior](#rendering-behavior)
3. [Format-Specific Implementations](#format-specific-implementations)
4. [Debugging Tips](#debugging-tips)

## Stitch Type Definitions

The pattern library defines five primary stitch types in `src/utilities/stitch-file-processing/pattern.ts`:

```typescript
export const stitchTypes = {
  normal: 0,
  jump: 1,
  trim: 2,
  stop: 4,
  end: 8,
}
```

### Normal Stitch (0)
- **Purpose**: Creates visible stitches on the fabric
- **Behavior**: Forms a continuous line of thread on the fabric
- **Visual Representation**: Connected line segments in the rendered design
- **When to Use**: For all standard embroidery stitches that should be visible

### Jump Stitch (1)
- **Purpose**: Moves the needle without creating a stitch
- **Behavior**: The machine raises the needle and moves to a new position without stitching
- **Visual Representation**: No line segment is drawn between the previous stitch and the jump stitch
- **When to Use**: When moving to a different area of the design without wanting a visible connection

### Trim Stitch (2)
- **Purpose**: Indicates where the thread should be cut
- **Behavior**: The machine cuts the thread and may move to a new position
- **Visual Representation**: No line segment is drawn between the previous stitch and the trim stitch
- **When to Use**: At the end of a section before moving to a disconnected section with the same thread color

### Stop Stitch (4)
- **Purpose**: Indicates a color change
- **Behavior**: The machine stops and waits for the thread to be changed
- **Visual Representation**: No line segment is drawn between the previous stitch and the stop stitch
- **When to Use**: When changing to a different thread color

### End Stitch (8)
- **Purpose**: Marks the end of the pattern
- **Behavior**: The machine stops and completes the embroidery process
- **Visual Representation**: Not rendered in the design
- **When to Use**: At the very end of the pattern

## Rendering Behavior

The rendering of stitches is handled by the `renderEmbroideryPatternPaths` function in `src/components/embroidery/FabricEmbroideryViewerUtils.ts`. This function processes the pattern's stitches and creates paths for each color section.

### Path Generation Logic

```typescript
// Process all stitches
for (let i = 0; i < pattern.stitches.length; i++) {
  const stitch = pattern.stitches[i]

  // Skip end stitches
  if ((stitch.flags & stitchTypes.end) === stitchTypes.end) {
    // Finalize any current path
    if (currentPath.length >= 2) {
      createAndAddPath(currentPath, currentColorIndex)
      currentPath = []
    }
    continue
  }

  // Calculate scaled coordinates
  const x = (stitch.x - pattern.left) * scale + offsetX
  const y = (stitch.y - pattern.top) * scale + offsetY

  // Handle color change
  if (currentColorIndex !== stitch.color) {
    // Finalize any current path before changing color
    if (currentPath.length >= 2) {
      createAndAddPath(currentPath, currentColorIndex)
    }

    // Update color and start a new path
    currentColorIndex = stitch.color
    currentPath = []

    // Only add this point if it's a normal stitch
    if (stitch.flags === 0) {
      currentPath.push({ x, y })
    }
    continue
  }

  // Check if this is a jump or trim stitch
  const isJumpOrTrim =
    (stitch.flags & stitchTypes.jump) === stitchTypes.jump ||
    (stitch.flags & stitchTypes.trim) === stitchTypes.trim

  // Check if this is a stop stitch (color change)
  const isStop = (stitch.flags & stitchTypes.stop) === stitchTypes.stop

  if (isJumpOrTrim) {
    // For jump/trim stitches, finalize the current path but don't add this point
    if (currentPath.length >= 2) {
      createAndAddPath(currentPath, currentColorIndex)
    }
    // Reset the path - we'll start a new one with the next normal stitch
    currentPath = []
  } else if (isStop) {
    // For stop stitches, finalize the current path but don't add this point
    if (currentPath.length >= 2) {
      createAndAddPath(currentPath, currentColorIndex)
    }
    // Reset the path - we'll start a new one with the next normal stitch
    currentPath = []
  } else {
    // This is a normal stitch - add it to the current path
    currentPath.push({ x, y })
  }
}
```

### Key Points:

1. **Normal Stitches**: Added to the current path, creating continuous line segments
2. **Jump/Trim Stitches**: Break the current path and start a new path at the next normal stitch
3. **Stop Stitches (Color Changes)**: Break the current path and start a new path with a new color
4. **End Stitches**: Not included in the rendered paths

## Format-Specific Implementations

### EXP Format

The EXP format uses special control codes to indicate different stitch types:

```typescript
// Handle special control codes
if (b0 === 0x80) {
  if ((b1 & 1) !== 0) {
    // Color change (STOP)
    // ...
    pattern.addStitchRel(dx / 10.0, -dy / 10.0, stitchTypes.stop, true)
  } else if (b1 === 0x02 || b1 === 0x04) {
    // Trim or Jump
    // ...
    const jumpOrTrimFlag = b1 === 0x04 ? stitchTypes.jump : stitchTypes.trim
    pattern.addStitchRel(dx / 10.0, -dy / 10.0, jumpOrTrimFlag, true)
  } else if (b1 === 0x80) {
    // Special case - trim with no movement
    // ...
    pattern.addStitchRel(0, 0, stitchTypes.trim, true)
  }
} else {
  // Normal stitch
  // ...
  pattern.addStitchRel(dx / 10.0, -dy / 10.0, stitchTypes.normal, true)
}
```

**Key Points:**
- `0x80, 0x01`: Color change (STOP)
- `0x80, 0x02`: Trim
- `0x80, 0x04`: Jump
- `0x80, 0x80`: Trim with no movement
- Y-axis is inverted (`-dy / 10.0`)

### JEF Format

The JEF format has a more complex structure with special handling for initial positioning:

```typescript
// Default to jump stitch during initial positioning
let flags = inInitialPositioning ? stitchTypes.jump : stitchTypes.normal

// Handle control codes
if (b0 === 0x80) {
  if (b1 & 0x01) {
    // Color change
    // ...
    flags = stitchTypes.stop
  } else if (b1 === 0x02 || b1 === 0x04) {
    // Trim stitch
    // ...
    flags = stitchTypes.trim
  } else if (b1 === 0x10) {
    // End of pattern
    pattern.addStitchRel(0, 0, stitchTypes.end, true)
  }
}
```

**Key Points:**
- Initial stitches are marked as jumps to avoid unwanted connections
- `0x80, 0x01`: Color change (STOP)
- `0x80, 0x02` or `0x80, 0x04`: Trim
- `0x80, 0x10`: End of pattern
- Pattern is inverted vertically at the end

### VP3 Format

The VP3 format has a more structured approach with explicit color sections:

```typescript
// The first stitch in each color section is typically a positioning stitch
// Mark it as a jump stitch to avoid drawing a line to it
const stitchFlag = isFirstStitch ? stitchTypes.jump : stitchTypes.normal

// Handle special codes
if (x === 0x80) {
  switch (y) {
    case 0x01: {
      // Jump stitch
      // ...
      pattern.addStitchRel(jumpX / 10.0, jumpY / 10.0, stitchTypes.jump, true)
    }
  }
} else {
  // Normal stitch
  pattern.addStitchRel(x / 10.0, y / 10.0, stitchFlag, true)
}

// Add stop stitch between colors
if (i + 1 < numberOfColors) {
  pattern.addStitchRel(0, 0, stitchTypes.stop, true)
}
```

**Key Points:**
- First stitch in each color section is marked as a jump
- `0x80, 0x01`: Jump stitch
- Stop stitches are added between color sections
- End stitch is added at the end of the pattern

## Debugging Tips

### Common Issues and Solutions

1. **Missing Line Segments**
   - **Symptom**: Parts of the design are not connected with line segments
   - **Cause**: Stitches incorrectly marked as jump/trim/stop
   - **Solution**: Ensure normal stitches are marked with `stitchTypes.normal`

2. **Unwanted Line Segments**
   - **Symptom**: Line segments appear where they shouldn't
   - **Cause**: Jump/trim/stop stitches incorrectly marked as normal
   - **Solution**: Properly mark special stitches with the appropriate type

3. **First Stitch Connected Incorrectly**
   - **Symptom**: Unwanted line segment from the origin to the first stitch
   - **Cause**: First stitch not marked as a jump
   - **Solution**: Mark the first stitch as `stitchTypes.jump`

4. **Inverted Pattern**
   - **Symptom**: Pattern appears upside down
   - **Cause**: Y-axis not inverted for formats that need it
   - **Solution**: Invert Y coordinates or use `pattern.invertPatternVertical()`

### Logging Stitch Information

Add detailed logging to help debug stitch type issues:

```typescript
console.log(`Added ${stitchTypeToString(flags)} stitch at ${stitchCount}: dx=${dx}, dy=${dy}`)

function stitchTypeToString(flags: number): string {
  if (flags === stitchTypes.normal) return 'normal'
  if (flags === stitchTypes.jump) return 'jump'
  if (flags === stitchTypes.trim) return 'trim'
  if (flags === stitchTypes.stop) return 'stop'
  if (flags === stitchTypes.end) return 'end'
  return `unknown(${flags})`
}
```

### Testing Stitch Type Handling

Create test patterns with different stitch types to verify rendering:

```typescript
// Create a test pattern with all stitch types
const pattern = new Pattern()
pattern.addColorRgb(0, 0, 0, 'Black')

// Normal stitches
pattern.addStitchAbs(0, 0, stitchTypes.normal)
pattern.addStitchAbs(10, 0, stitchTypes.normal)
pattern.addStitchAbs(20, 0, stitchTypes.normal)

// Jump stitch
pattern.addStitchAbs(50, 0, stitchTypes.jump)

// Normal stitches after jump
pattern.addStitchAbs(60, 0, stitchTypes.normal)
pattern.addStitchAbs(70, 0, stitchTypes.normal)

// Trim stitch
pattern.addStitchAbs(70, 0, stitchTypes.trim)

// Normal stitches after trim
pattern.addStitchAbs(70, 20, stitchTypes.normal)
pattern.addStitchAbs(80, 20, stitchTypes.normal)

// Color change
pattern.addStitchAbs(80, 20, stitchTypes.stop, true)
pattern.addColorRgb(255, 0, 0, 'Red')

// Normal stitches in new color
pattern.addStitchAbs(80, 30, stitchTypes.normal)
pattern.addStitchAbs(90, 30, stitchTypes.normal)

// End stitch
pattern.addStitchAbs(90, 30, stitchTypes.end)
```

## Conclusion

Properly handling stitch types is crucial for correctly rendering embroidery patterns. By marking stitches with the appropriate type flags and understanding how the rendering logic processes them, you can ensure that patterns are displayed correctly with the right connections between stitches.
