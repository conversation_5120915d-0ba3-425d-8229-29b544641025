# Embroidery Stitch Type Diagrams

This document provides visual diagrams to help understand how different stitch types are rendered in the StitchEstimate pattern library.

## Basic Stitch Type Visualization

The following diagrams use these symbols:
- `•` represents a stitch point
- `—` represents a visible line segment (thread)
- `···` represents a jump or movement without a visible thread
- `✂` represents a trim point
- `🎨` represents a color change (stop)

### Normal Stitches

Normal stitches are connected with visible line segments:

```
•—•—•—•—•—•
```

### Jump Stitches

Jump stitches create a break in the visible line segments:

```
•—•—•···•—•—•
     ↑
   Jump
```

### Trim Stitches

Trim stitches indicate where the thread should be cut:

```
•—•—•✂···•—•—•
     ↑
   Trim
```

### Stop Stitches (Color Changes)

Stop stitches indicate a color change:

```
•—•—•🎨•—•—•
     ↑
  Color
 Change
```

## Complex Pattern Examples

### Pattern with Multiple Stitch Types

```
Color 1:  •—•—•—•—•✂
                   ···
                      •—•—•—•
                             ···
Color 2:                        •🎨•—•—•—•
                                       ···
                                          •—•—•
```

### Pattern with Jumps Between Sections

```
                 Jump
                  ↓
Color 1:  •—•—•—•···•—•—•—•
                       ↑
                     Jump
                      ↓
                      ···•—•—•—•
```

## Rendering Process Visualization

This diagram shows how the rendering process handles different stitch types:

```
Input Stitches:  N  N  N  J  N  N  T  N  N  S  N  N  E
                 |  |  |  |  |  |  |  |  |  |  |  |  |
                 v  v  v  v  v  v  v  v  v  v  v  v  v
                 •→•→•  •→•→•  •→•  •→•→•
                 |     |     |    |
                 |     |     |    |
Output Paths:    Path1 Path2 Path3 Path4
```

Where:
- N = Normal Stitch
- J = Jump Stitch
- T = Trim Stitch
- S = Stop Stitch (Color Change)
- E = End Stitch

## Real-World Pattern Example

Here's a simplified visualization of a real-world pattern with multiple colors and stitch types:

```
Color 1 (Black):
  •—•—•—•—•—•—•—•—•
  |               |
  |               |
  •               •
  |               |
  |               |
  •—•—•—•—•—•—•—•—•

Color 2 (Red):
          •—•—•—•
         /       \
        /         \
       •           •
        \         /
         \       /
          •—•—•—•

Color 3 (Blue):
  •   •   •   •   •
   \ / \ / \ / \ /
    •   •   •   •
   / \ / \ / \ / \
  •   •   •   •   •
```

## Stitch Type Decision Tree

When implementing a format parser, use this decision tree to determine the appropriate stitch type:

```
Is this the end of the pattern?
├── Yes → Mark as END stitch
└── No → Continue

Is this a color change?
├── Yes → Mark as STOP stitch
└── No → Continue

Is this a thread cut command?
├── Yes → Mark as TRIM stitch
└── No → Continue

Is this a large movement (e.g., > 50 units)?
├── Yes → Mark as JUMP stitch
└── No → Mark as NORMAL stitch
```

## Format-Specific Stitch Identification

### EXP Format

```
Read byte b0, b1
Is b0 == 0x80?
├── Yes → Check b1:
│   ├── b1 & 0x01 != 0 → Color change (STOP)
│   ├── b1 == 0x02 → Trim
│   ├── b1 == 0x04 → Jump
│   └── b1 == 0x80 → Trim with no movement
└── No → Normal stitch with dx=b0, dy=b1
```

### JEF Format

```
Read stitch data
Is this the first stitch?
├── Yes → Mark as JUMP
└── No → Continue

Check control bits:
├── 0x10 → Color change (STOP)
├── 0x20 → Jump
├── 0x40 → End
└── None of above → Normal stitch
```

## Troubleshooting Guide

If your pattern is not rendering correctly, check these common issues:

1. **Missing Line Segments**
   ```
   Expected: •—•—•—•
   Actual:   •  •  •
   ```
   Solution: Check if stitches are incorrectly marked as jump/trim

2. **Unwanted Line Segments**
   ```
   Expected: •—•···•—•
   Actual:   •—•—•—•
   ```
   Solution: Check if jumps/trims are incorrectly marked as normal

3. **Incorrect First Stitch Connection**
   ```
   Expected: •···•—•—•
   Actual:   •—•—•—•
   ```
   Solution: Mark first stitch as jump if needed

4. **Inverted Pattern**
   ```
   Expected:    •
              / \
             •   •
             |   |
             •—•—•
   
   Actual:   •—•—•
             |   |
             •   •
              \ /
               •
   ```
   Solution: Invert Y coordinates or use `pattern.invertPatternVertical()`
