"use strict";
/**
 * Embroidery Processor Package
 *
 * This package provides functionality for processing embroidery files
 * that can be used by both the frontend and batch processing scripts.
 *
 * The core module is designed to work in both browser and Node.js environments,
 * providing a consistent API for processing embroidery files regardless of the
 * execution environment.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
// Export core functionality
__exportStar(require("./core"), exports);
// Export Pattern class and related types
__exportStar(require("./core/pattern"), exports);
// Export types
__exportStar(require("./types"), exports);
// Export format readers
__exportStar(require("./formats"), exports);
// Export browser-specific functionality
__exportStar(require("./browser"), exports);
//# sourceMappingURL=index.js.map