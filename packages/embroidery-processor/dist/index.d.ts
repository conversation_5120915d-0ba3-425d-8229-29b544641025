/**
 * Embroidery Processor Package
 *
 * This package provides functionality for processing embroidery files
 * that can be used by both the frontend and batch processing scripts.
 *
 * The core module is designed to work in both browser and Node.js environments,
 * providing a consistent API for processing embroidery files regardless of the
 * execution environment.
 */
export * from './core';
export * from './core/pattern';
export * from './formats';
export * from './browser';
