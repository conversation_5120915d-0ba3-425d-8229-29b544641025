"use strict";
/**
 * Core Embroidery File Processing Module
 *
 * This module provides shared functionality for processing embroidery files
 * that can be used by both the frontend and batch processing scripts.
 *
 * The core module is designed to work in both browser and Node.js environments,
 * providing a consistent API for processing embroidery files regardless of the
 * execution environment.
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SUPPORTED_FORMATS = exports.FILE_FORMAT_MAP = void 0;
exports.isSupportedEmbroideryFormat = isSupportedEmbroideryFormat;
exports.processEmbroideryBuffer = processEmbroideryBuffer;
exports.extractMetadata = extractMetadata;
exports.renderPatternOnContext = renderPatternOnContext;
exports.calculatePatternScale = calculatePatternScale;
var pattern_1 = require("./pattern");
var formats_1 = require("../formats");
var logger_1 = require("../utils/logger");
// Create a module-specific logger
var log = logger_1.logger.createFormatLogger('core');
/**
 * Map of file extensions to their respective reader functions
 */
exports.FILE_FORMAT_MAP = {
    '.dst': formats_1.dstRead,
    dst: formats_1.dstRead,
    '.pes': formats_1.pesRead,
    pes: formats_1.pesRead,
    '.pec': formats_1.pesRead, // PEC is embedded in PES, so we use the same parser
    pec: formats_1.pesRead, // PEC is embedded in PES, so we use the same parser
    '.jef': formats_1.jefRead,
    jef: formats_1.jefRead,
    '.exp': formats_1.expRead,
    exp: formats_1.expRead,
    '.vp3': formats_1.vp3Read,
    vp3: formats_1.vp3Read,
};
/**
 * List of supported file formats (without the leading dot)
 */
exports.SUPPORTED_FORMATS = Object.keys(exports.FILE_FORMAT_MAP)
    .filter(function (ext) { return ext.startsWith('.'); }) // Only take the ones with dots
    .map(function (ext) { return ext.substring(1); }); // Remove the dot
/**
 * Check if a file format is supported
 * @param filename The filename to check
 * @returns True if the file format is supported
 */
function isSupportedEmbroideryFormat(filename) {
    var dotPosition = filename.lastIndexOf('.');
    var extension = dotPosition >= 0 ? filename.substring(dotPosition).toLowerCase() : '';
    return extension in exports.FILE_FORMAT_MAP;
}
/**
 * Process an ArrayBuffer containing embroidery file data
 * @param arrayBuffer The ArrayBuffer containing the file data
 * @param filename The original filename (used for extension detection)
 * @returns A Promise that resolves to a Pattern object
 */
function processEmbroideryBuffer(arrayBuffer, filename) {
    return __awaiter(this, void 0, void 0, function () {
        var dotPosition, extension, pattern, reader;
        return __generator(this, function (_a) {
            dotPosition = filename.lastIndexOf('.');
            extension = dotPosition >= 0 ? filename.substring(dotPosition).toLowerCase() : '';
            // Check if this is a supported format
            if (!(extension in exports.FILE_FORMAT_MAP)) {
                throw new Error("Unsupported file format: ".concat(extension));
            }
            pattern = new pattern_1.Pattern();
            try {
                reader = exports.FILE_FORMAT_MAP[extension];
                // Read the file
                reader(arrayBuffer, pattern);
                // Calculate the bounding box
                pattern.calculateBoundingBox();
                return [2 /*return*/, pattern];
            }
            catch (error) {
                log.error("Error processing embroidery file: ".concat(filename), error);
                throw error;
            }
            return [2 /*return*/];
        });
    });
}
/**
 * Extract metadata from a pattern
 * @param pattern The pattern to extract metadata from
 * @param filename The original filename
 * @param unit Optional unit for width/height (defaults to 'px')
 * @returns Metadata about the embroidery file
 */
function extractMetadata(pattern, filename, unit) {
    if (unit === void 0) { unit = 'px'; }
    // Get the file extension
    var dotPosition = filename.lastIndexOf('.');
    var extension = dotPosition >= 0 ? filename.substring(dotPosition).toLowerCase() : '';
    var format = extension.startsWith('.') ? extension.substring(1) : extension;
    // Calculate dimensions
    var width = pattern.right - pattern.left;
    var height = pattern.bottom - pattern.top;
    // Create width/height objects if unit is provided
    var widthValue = unit ? { value: width, unit: unit } : width;
    var heightValue = unit ? { value: height, unit: unit } : height;
    return {
        format: format,
        width: widthValue,
        height: heightValue,
        stitchCount: pattern.stitches.length,
        colorCount: pattern.colors.length,
        filename: filename,
    };
}
/**
 * Render a pattern on a canvas context
 * @param ctx The canvas context to render on
 * @param pattern The pattern to render
 * @param scale The scale factor to apply
 * @param lineWidth The line width to use
 * @param use3dEffect Whether to use a 3D effect
 */
function renderPatternOnContext(ctx, // GenericCanvasContext
pattern, scale, lineWidth, use3dEffect) {
    if (scale === void 0) { scale = 1.0; }
    if (lineWidth === void 0) { lineWidth = 1.0; }
    if (use3dEffect === void 0) { use3dEffect = false; }
    var stitchCount = pattern.stitches.length;
    if (stitchCount === 0)
        return;
    // Save the current context state
    ctx.save();
    // Set line properties
    ctx.lineWidth = lineWidth;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    // Apply scaling
    ctx.scale(scale, scale);
    var lastX = pattern.stitches[0].x;
    var lastY = pattern.stitches[0].y;
    var lastColor = pattern.stitches[0].color;
    var currentColor = pattern.colors[lastColor];
    // Set initial color
    ctx.strokeStyle = "rgb(".concat(currentColor.r, ", ").concat(currentColor.g, ", ").concat(currentColor.b, ")");
    // Apply 3D effect if requested
    if (use3dEffect) {
        ctx.shadowBlur = 2;
        ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
        ctx.shadowOffsetX = 0.5;
        ctx.shadowOffsetY = 0.5;
    }
    // Start the path
    ctx.beginPath();
    ctx.moveTo(lastX, lastY);
    // Draw the stitches
    for (var i = 1; i < stitchCount; i++) {
        var stitch = pattern.stitches[i];
        var x = stitch.x, y = stitch.y, flags = stitch.flags, color = stitch.color;
        // Check if this is a color change or special stitch
        if (color !== lastColor || (flags & 0xf) !== 0) {
            // Stroke the current path
            ctx.stroke();
            // Start a new path
            ctx.beginPath();
            ctx.moveTo(lastX, lastY);
            // Update color if needed
            if (color !== lastColor) {
                lastColor = color;
                currentColor = pattern.colors[color];
                ctx.strokeStyle = "rgb(".concat(currentColor.r, ", ").concat(currentColor.g, ", ").concat(currentColor.b, ")");
            }
            // Skip drawing for jump, trim, stop, or end stitches
            if ((flags & 0xf) !== 0) {
                lastX = x;
                lastY = y;
                ctx.moveTo(x, y);
                continue;
            }
        }
        // Draw line to the current stitch
        ctx.lineTo(x, y);
        lastX = x;
        lastY = y;
    }
    // Stroke any remaining path
    ctx.stroke();
    // Restore the context state
    ctx.restore();
}
/**
 * Calculate the scale factor to fit a pattern within a given width and height
 * @param pattern The pattern to scale
 * @param maxWidth The maximum width
 * @param maxHeight The maximum height
 * @param padding Optional padding (default: 10)
 * @returns The scale factor
 */
function calculatePatternScale(pattern, maxWidth, maxHeight, padding) {
    if (padding === void 0) { padding = 10; }
    var patternWidth = pattern.right - pattern.left;
    var patternHeight = pattern.bottom - pattern.top;
    if (patternWidth === 0 || patternHeight === 0) {
        return 1.0;
    }
    var availableWidth = maxWidth - padding * 2;
    var availableHeight = maxHeight - padding * 2;
    var widthScale = availableWidth / patternWidth;
    var heightScale = availableHeight / patternHeight;
    // Return the smaller scale to ensure the pattern fits
    return Math.min(widthScale, heightScale);
}
//# sourceMappingURL=index.js.map