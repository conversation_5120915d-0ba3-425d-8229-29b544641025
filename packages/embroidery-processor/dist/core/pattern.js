"use strict";
/**
 * Pattern - Implementation of the embroidery pattern class
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Pattern = exports.stitchTypes = exports.Color = exports.Stitch = void 0;
var embroidery_1 = require("../types/embroidery");
Object.defineProperty(exports, "Stitch", { enumerable: true, get: function () { return embroidery_1.Stitch; } });
Object.defineProperty(exports, "Color", { enumerable: true, get: function () { return embroidery_1.Color; } });
Object.defineProperty(exports, "stitchTypes", { enumerable: true, get: function () { return embroidery_1.stitchTypes; } });
/**
 * Pattern class for representing an embroidery design
 */
var Pattern = /** @class */ (function () {
    function Pattern() {
        this.colors = [];
        this.stitches = [];
        this.hoop = {};
        this.lastX = 0;
        this.lastY = 0;
        this.top = 0;
        this.bottom = 0;
        this.left = 0;
        this.right = 0;
        this.currentColorIndex = 0;
    }
    /**
     * Add a color to the pattern
     */
    Pattern.prototype.addColor = function (color) {
        this.colors[this.colors.length] = color;
    };
    /**
     * Add a color specified by RGB values
     */
    Pattern.prototype.addColorRgb = function (r, g, b, description) {
        if (description === void 0) { description = ''; }
        this.colors[this.colors.length] = new embroidery_1.Color(r, g, b, description);
    };
    /**
     * Add a stitch with absolute coordinates
     */
    Pattern.prototype.addStitchAbs = function (x, y, flags, isAutoColorIndex) {
        if (isAutoColorIndex === void 0) { isAutoColorIndex = false; }
        if ((flags & embroidery_1.stitchTypes.end) === embroidery_1.stitchTypes.end) {
            this.calculateBoundingBox();
            this.fixColorCount();
        }
        if ((flags & embroidery_1.stitchTypes.stop) === embroidery_1.stitchTypes.stop && this.stitches.length === 0) {
            return;
        }
        if ((flags & embroidery_1.stitchTypes.stop) === embroidery_1.stitchTypes.stop && isAutoColorIndex) {
            this.currentColorIndex += 1;
        }
        this.stitches[this.stitches.length] = new embroidery_1.Stitch(x, y, flags, this.currentColorIndex);
    };
    /**
     * Add a stitch with relative coordinates
     */
    Pattern.prototype.addStitchRel = function (dx, dy, flags, isAutoColorIndex) {
        if (isAutoColorIndex === void 0) { isAutoColorIndex = false; }
        if (this.stitches.length !== 0) {
            var nx = this.lastX + dx;
            var ny = this.lastY + dy;
            this.lastX = nx;
            this.lastY = ny;
            this.addStitchAbs(nx, ny, flags, isAutoColorIndex);
        }
        else {
            // For the first stitch, use the coordinates as absolute
            this.addStitchAbs(dx, dy, flags, isAutoColorIndex);
            // Update lastX and lastY for the first stitch to ensure proper relative positioning
            this.lastX = dx;
            this.lastY = dy;
        }
    };
    /**
     * Calculate the bounding box of the pattern
     */
    Pattern.prototype.calculateBoundingBox = function () {
        var stitchCount = this.stitches.length;
        if (stitchCount === 0)
            return;
        var minX = Number.MAX_VALUE;
        var minY = Number.MAX_VALUE;
        var maxX = Number.MIN_VALUE;
        var maxY = Number.MIN_VALUE;
        for (var i = 0; i < stitchCount; i++) {
            var _a = this.stitches[i], x = _a.x, y = _a.y;
            minX = Math.min(minX, x);
            minY = Math.min(minY, y);
            maxX = Math.max(maxX, x);
            maxY = Math.max(maxY, y);
        }
        this.left = minX;
        this.top = minY;
        this.right = maxX;
        this.bottom = maxY;
    };
    /**
     * Move the pattern to positive coordinates
     */
    Pattern.prototype.moveToPositive = function () {
        var stitchCount = this.stitches.length;
        for (var i = 0; i < stitchCount; i++) {
            this.stitches[i].x -= this.left;
            this.stitches[i].y -= this.top;
        }
        this.right -= this.left;
        this.left = 0;
        this.bottom -= this.top;
        this.top = 0;
    };
    /**
     * Invert the pattern vertically
     */
    Pattern.prototype.invertPatternVertical = function () {
        var temp = -this.top;
        var stitchCount = this.stitches.length;
        for (var i = 0; i < stitchCount; i++) {
            this.stitches[i].y = -this.stitches[i].y;
        }
        this.top = -this.bottom;
        this.bottom = temp;
    };
    /**
     * Add a random color
     */
    Pattern.prototype.addColorRandom = function () {
        this.colors[this.colors.length] = new embroidery_1.Color(Math.round(Math.random() * 256), Math.round(Math.random() * 256), Math.round(Math.random() * 256), 'random');
    };
    /**
     * Fix the color count based on stitches
     */
    Pattern.prototype.fixColorCount = function () {
        var maxColorIndex = 0;
        var stitchCount = this.stitches.length;
        for (var i = 0; i < stitchCount; i++) {
            maxColorIndex = Math.max(maxColorIndex, this.stitches[i].color);
        }
        while (this.colors.length <= maxColorIndex) {
            this.addColorRandom();
        }
        this.colors.splice(maxColorIndex + 1, this.colors.length - maxColorIndex - 1);
    };
    /**
     * Calculate the width of the pattern
     */
    Pattern.prototype.calculateWidth = function () {
        return Math.max(1, this.right - this.left);
    };
    /**
     * Calculate the height of the pattern
     */
    Pattern.prototype.calculateHeight = function () {
        return Math.max(1, this.bottom - this.top);
    };
    /**
     * Draw the pattern on a canvas
     */
    Pattern.prototype.drawShape = function (canvas, lineWidth, use3DEffect, shadowBlur, resolutionScale) {
        if (lineWidth === void 0) { lineWidth = 1; }
        if (use3DEffect === void 0) { use3DEffect = false; }
        if (shadowBlur === void 0) { shadowBlur = 4; }
        if (resolutionScale === void 0) { resolutionScale = 1; }
        // Get the original canvas dimensions from the element's attributes
        var canvasWidth = canvas.width;
        var canvasHeight = canvas.height;
        // Store the pattern dimensions
        var patternWidth = this.right;
        var patternHeight = this.bottom;
        // Calculate the aspect ratio of the pattern
        var patternRatio = patternWidth / patternHeight;
        // Safety check: limit resolution scaling to prevent exceeding browser limits
        // Most browsers have a max canvas size of around 16,384 pixels in any dimension
        var MAX_CANVAS_DIMENSION = 8192; // Set a safe limit below most browser maximums
        var safeResolutionScale = Math.min(resolutionScale, MAX_CANVAS_DIMENSION / canvasWidth, MAX_CANVAS_DIMENSION / canvasHeight);
        // If we had to adjust the scale, log a warning
        if (safeResolutionScale < resolutionScale) {
            console.warn("Resolution scale reduced from ".concat(resolutionScale, " to ").concat(safeResolutionScale, " to prevent exceeding maximum canvas dimensions"));
        }
        // Apply resolution scaling while preserving the original canvas dimensions
        canvas.width = canvasWidth * safeResolutionScale;
        canvas.height = canvasHeight * safeResolutionScale;
        // Don't modify the CSS dimensions - React is already handling this
        if (canvas.getContext) {
            var ctx = canvas.getContext('2d');
            if (!ctx)
                return;
            // Scale according to resolution scale
            ctx.scale(safeResolutionScale, safeResolutionScale);
            // Calculate the fit scaling to maintain aspect ratio
            var canvasRatio = canvasWidth / canvasHeight;
            var scale = void 0;
            var offsetX = 0;
            var offsetY = 0;
            if (patternRatio > canvasRatio) {
                // Pattern is wider than canvas
                scale = canvasWidth / patternWidth;
                offsetY = (canvasHeight - patternHeight * scale) / 2;
            }
            else {
                // Pattern is taller than canvas
                scale = canvasHeight / patternHeight;
                offsetX = (canvasWidth - patternWidth * scale) / 2;
            }
            // Apply the fit scaling and centering
            ctx.translate(offsetX, offsetY);
            ctx.scale(scale, scale);
            ctx.lineJoin = 'round';
            ctx.lineCap = 'round';
            if (this.stitches.length === 0)
                return;
            if (use3DEffect) {
                this.drawStitchLayer(ctx, lineWidth * 1.7, true, {
                    shadowBlur: shadowBlur * 1.8,
                    shadowColor: 'rgba(0,0,0,0.8)',
                    shadowOffsetX: lineWidth * 0.5,
                    shadowOffsetY: lineWidth * 0.5,
                    colorAdjust: -90,
                });
                this.drawStitchLayer(ctx, lineWidth * 1.3, false, {
                    shadowBlur: 0,
                    shadowColor: 'transparent',
                    shadowOffsetX: 0,
                    shadowOffsetY: 0,
                    colorAdjust: -50,
                });
                this.drawStitchLayer(ctx, lineWidth, false, {
                    shadowBlur: 0,
                    shadowColor: 'transparent',
                    shadowOffsetX: 0,
                    shadowOffsetY: 0,
                    colorAdjust: 0,
                    useHighlight: true,
                });
            }
            else {
                ctx.lineWidth = lineWidth;
                ctx.beginPath();
                var color = this.colors[this.stitches[0].color];
                ctx.strokeStyle = "rgb(".concat(color.r, ",").concat(color.g, ",").concat(color.b, ")");
                for (var i = 0; i < this.stitches.length; i++) {
                    var currentStitch = this.stitches[i];
                    if (currentStitch.flags === embroidery_1.stitchTypes.jump ||
                        currentStitch.flags === embroidery_1.stitchTypes.trim ||
                        currentStitch.flags === embroidery_1.stitchTypes.stop) {
                        ctx.stroke();
                        var color_1 = this.colors[currentStitch.color];
                        ctx.beginPath();
                        ctx.strokeStyle = "rgb(".concat(color_1.r, ",").concat(color_1.g, ",").concat(color_1.b, ")");
                        ctx.moveTo(currentStitch.x, currentStitch.y);
                    }
                    ctx.lineTo(currentStitch.x, currentStitch.y);
                }
                ctx.stroke();
            }
        }
        else {
            if (typeof window !== 'undefined' && window.alert) {
                window.alert('You need Safari or Firefox 1.5+ to see this demo.');
            }
        }
    };
    Pattern.prototype.drawStitchLayer = function (ctx, lineWidth, isBaseShadow, settings) {
        ctx.save();
        ctx.lineWidth = lineWidth;
        ctx.shadowBlur = settings.shadowBlur;
        ctx.shadowColor = settings.shadowColor;
        ctx.shadowOffsetX = settings.shadowOffsetX;
        ctx.shadowOffsetY = settings.shadowOffsetY;
        ctx.beginPath();
        var initialColor = this.colors[this.stitches[0].color];
        if (settings.useHighlight) {
            this.setStitchGradient(ctx, initialColor, lineWidth);
        }
        else {
            var adjustedColor = this.adjustColor(initialColor, settings.colorAdjust);
            ctx.strokeStyle = "rgb(".concat(adjustedColor.r, ",").concat(adjustedColor.g, ",").concat(adjustedColor.b, ")");
        }
        for (var i = 0; i < this.stitches.length; i++) {
            var currentStitch = this.stitches[i];
            if (currentStitch.flags === embroidery_1.stitchTypes.jump ||
                currentStitch.flags === embroidery_1.stitchTypes.trim ||
                currentStitch.flags === embroidery_1.stitchTypes.stop) {
                ctx.stroke();
                var color = this.colors[currentStitch.color];
                ctx.beginPath();
                if (settings.useHighlight) {
                    this.setStitchGradient(ctx, color, lineWidth);
                }
                else {
                    var adjustedColor = this.adjustColor(color, settings.colorAdjust);
                    ctx.strokeStyle = "rgb(".concat(adjustedColor.r, ",").concat(adjustedColor.g, ",").concat(adjustedColor.b, ")");
                }
                ctx.moveTo(currentStitch.x, currentStitch.y);
            }
            ctx.lineTo(currentStitch.x, currentStitch.y);
        }
        ctx.stroke();
        ctx.restore();
    };
    Pattern.prototype.adjustColor = function (color, amount) {
        return {
            r: Math.max(0, Math.min(255, color.r + amount)),
            g: Math.max(0, Math.min(255, color.g + amount)),
            b: Math.max(0, Math.min(255, color.b + amount)),
        };
    };
    Pattern.prototype.setStitchGradient = function (ctx, color, lineWidth) {
        var gradient = ctx.createLinearGradient(0, 0, lineWidth, lineWidth);
        gradient.addColorStop(0, "rgb(".concat(Math.min(255, color.r + 70), ",").concat(Math.min(255, color.g + 70), ",").concat(Math.min(255, color.b + 70), ")"));
        gradient.addColorStop(0.3, "rgb(".concat(Math.min(255, color.r + 15), ",").concat(Math.min(255, color.g + 15), ",").concat(Math.min(255, color.b + 15), ")"));
        gradient.addColorStop(0.5, "rgb(".concat(color.r, ",").concat(color.g, ",").concat(color.b, ")"));
        gradient.addColorStop(0.7, "rgb(".concat(Math.max(0, color.r - 30), ",").concat(Math.max(0, color.g - 30), ",").concat(Math.max(0, color.b - 30), ")"));
        gradient.addColorStop(1, "rgb(".concat(Math.max(0, color.r - 100), ",").concat(Math.max(0, color.g - 100), ",").concat(Math.max(0, color.b - 100), ")"));
        ctx.strokeStyle = gradient;
        if (Math.random() > 0.7) {
            ctx.setLineDash([lineWidth * 0.8, lineWidth * 0.2]);
        }
        else {
            ctx.setLineDash([]);
        }
    };
    return Pattern;
}());
exports.Pattern = Pattern;
//# sourceMappingURL=pattern.js.map