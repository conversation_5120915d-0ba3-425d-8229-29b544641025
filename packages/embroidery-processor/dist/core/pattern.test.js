"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var vitest_1 = require("vitest");
var pattern_1 = require("./pattern");
(0, vitest_1.describe)('Pattern', function () {
    (0, vitest_1.it)('should create a new pattern with default values', function () {
        var pattern = new pattern_1.Pattern();
        (0, vitest_1.expect)(pattern.colors).toEqual([]);
        (0, vitest_1.expect)(pattern.stitches).toEqual([]);
        (0, vitest_1.expect)(pattern.lastX).toBe(0);
        (0, vitest_1.expect)(pattern.lastY).toBe(0);
        (0, vitest_1.expect)(pattern.top).toBe(0);
        (0, vitest_1.expect)(pattern.bottom).toBe(0);
        (0, vitest_1.expect)(pattern.left).toBe(0);
        (0, vitest_1.expect)(pattern.right).toBe(0);
        (0, vitest_1.expect)(pattern.currentColorIndex).toBe(0);
    });
    (0, vitest_1.it)('should add a color to the pattern', function () {
        var pattern = new pattern_1.Pattern();
        pattern.addColorRgb(255, 0, 0, 'Red');
        (0, vitest_1.expect)(pattern.colors.length).toBe(1);
        (0, vitest_1.expect)(pattern.colors[0].r).toBe(255);
        (0, vitest_1.expect)(pattern.colors[0].g).toBe(0);
        (0, vitest_1.expect)(pattern.colors[0].b).toBe(0);
        (0, vitest_1.expect)(pattern.colors[0].description).toBe('Red');
    });
    (0, vitest_1.it)('should add a stitch with absolute coordinates', function () {
        var pattern = new pattern_1.Pattern();
        pattern.addStitchAbs(10, 20, pattern_1.stitchTypes.normal);
        (0, vitest_1.expect)(pattern.stitches.length).toBe(1);
        (0, vitest_1.expect)(pattern.stitches[0].x).toBe(10);
        (0, vitest_1.expect)(pattern.stitches[0].y).toBe(20);
        (0, vitest_1.expect)(pattern.stitches[0].flags).toBe(pattern_1.stitchTypes.normal);
        (0, vitest_1.expect)(pattern.stitches[0].color).toBe(0);
    });
    (0, vitest_1.it)('should add a stitch with relative coordinates', function () {
        var pattern = new pattern_1.Pattern();
        // First stitch is treated as absolute
        pattern.addStitchRel(10, 20, pattern_1.stitchTypes.normal);
        // Second stitch is relative to the first
        pattern.addStitchRel(5, 10, pattern_1.stitchTypes.normal);
        (0, vitest_1.expect)(pattern.stitches.length).toBe(2);
        (0, vitest_1.expect)(pattern.stitches[0].x).toBe(10);
        (0, vitest_1.expect)(pattern.stitches[0].y).toBe(20);
        (0, vitest_1.expect)(pattern.stitches[1].x).toBe(15);
        (0, vitest_1.expect)(pattern.stitches[1].y).toBe(30);
    });
    (0, vitest_1.it)('should calculate the bounding box correctly', function () {
        var pattern = new pattern_1.Pattern();
        pattern.addStitchAbs(10, 20, pattern_1.stitchTypes.normal);
        pattern.addStitchAbs(30, 40, pattern_1.stitchTypes.normal);
        pattern.addStitchAbs(5, 15, pattern_1.stitchTypes.normal);
        pattern.calculateBoundingBox();
        (0, vitest_1.expect)(pattern.left).toBe(5);
        (0, vitest_1.expect)(pattern.top).toBe(15);
        (0, vitest_1.expect)(pattern.right).toBe(30);
        (0, vitest_1.expect)(pattern.bottom).toBe(40);
    });
    (0, vitest_1.it)('should move the pattern to positive coordinates', function () {
        var pattern = new pattern_1.Pattern();
        pattern.addStitchAbs(-10, -20, pattern_1.stitchTypes.normal);
        pattern.addStitchAbs(30, 40, pattern_1.stitchTypes.normal);
        pattern.calculateBoundingBox();
        pattern.moveToPositive();
        (0, vitest_1.expect)(pattern.left).toBe(0);
        (0, vitest_1.expect)(pattern.top).toBe(0);
        (0, vitest_1.expect)(pattern.right).toBe(40);
        (0, vitest_1.expect)(pattern.bottom).toBe(60);
        (0, vitest_1.expect)(pattern.stitches[0].x).toBe(0);
        (0, vitest_1.expect)(pattern.stitches[0].y).toBe(0);
        (0, vitest_1.expect)(pattern.stitches[1].x).toBe(40);
        (0, vitest_1.expect)(pattern.stitches[1].y).toBe(60);
    });
    (0, vitest_1.it)('should invert the pattern vertically', function () {
        var pattern = new pattern_1.Pattern();
        pattern.addStitchAbs(10, 20, pattern_1.stitchTypes.normal);
        pattern.addStitchAbs(30, 40, pattern_1.stitchTypes.normal);
        pattern.calculateBoundingBox();
        pattern.invertPatternVertical();
        (0, vitest_1.expect)(pattern.top).toBe(-40);
        (0, vitest_1.expect)(pattern.bottom).toBe(-20);
        (0, vitest_1.expect)(pattern.stitches[0].y).toBe(-20);
        (0, vitest_1.expect)(pattern.stitches[1].y).toBe(-40);
    });
    (0, vitest_1.it)('should fix the color count based on stitches', function () {
        var pattern = new pattern_1.Pattern();
        // Add a stitch with color index 2 (but no colors exist yet)
        pattern.currentColorIndex = 2;
        pattern.addStitchAbs(10, 20, pattern_1.stitchTypes.normal);
        // Fix the color count
        pattern.fixColorCount();
        // Should have 3 colors (indexes 0, 1, 2)
        (0, vitest_1.expect)(pattern.colors.length).toBe(3);
    });
});
