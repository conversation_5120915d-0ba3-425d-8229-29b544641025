import { describe, it, expect } from 'vitest';
import { Pattern, stitchTypes } from './pattern';
describe('Pattern', () => {
    it('should create a new pattern with default values', () => {
        const pattern = new Pattern();
        expect(pattern.colors).toEqual([]);
        expect(pattern.stitches).toEqual([]);
        expect(pattern.lastX).toBe(0);
        expect(pattern.lastY).toBe(0);
        expect(pattern.top).toBe(0);
        expect(pattern.bottom).toBe(0);
        expect(pattern.left).toBe(0);
        expect(pattern.right).toBe(0);
        expect(pattern.currentColorIndex).toBe(0);
    });
    it('should add a color to the pattern', () => {
        const pattern = new Pattern();
        pattern.addColorRgb(255, 0, 0, 'Red');
        expect(pattern.colors.length).toBe(1);
        expect(pattern.colors[0].r).toBe(255);
        expect(pattern.colors[0].g).toBe(0);
        expect(pattern.colors[0].b).toBe(0);
        expect(pattern.colors[0].description).toBe('Red');
    });
    it('should add a stitch with absolute coordinates', () => {
        const pattern = new Pattern();
        pattern.addStitchAbs(10, 20, stitchTypes.normal);
        expect(pattern.stitches.length).toBe(1);
        expect(pattern.stitches[0].x).toBe(10);
        expect(pattern.stitches[0].y).toBe(20);
        expect(pattern.stitches[0].flags).toBe(stitchTypes.normal);
        expect(pattern.stitches[0].color).toBe(0);
    });
    it('should add a stitch with relative coordinates', () => {
        const pattern = new Pattern();
        // First stitch is treated as absolute
        pattern.addStitchRel(10, 20, stitchTypes.normal);
        // Second stitch is relative to the first
        pattern.addStitchRel(5, 10, stitchTypes.normal);
        expect(pattern.stitches.length).toBe(2);
        expect(pattern.stitches[0].x).toBe(10);
        expect(pattern.stitches[0].y).toBe(20);
        expect(pattern.stitches[1].x).toBe(15);
        expect(pattern.stitches[1].y).toBe(30);
    });
    it('should calculate the bounding box correctly', () => {
        const pattern = new Pattern();
        pattern.addStitchAbs(10, 20, stitchTypes.normal);
        pattern.addStitchAbs(30, 40, stitchTypes.normal);
        pattern.addStitchAbs(5, 15, stitchTypes.normal);
        pattern.calculateBoundingBox();
        expect(pattern.left).toBe(5);
        expect(pattern.top).toBe(15);
        expect(pattern.right).toBe(30);
        expect(pattern.bottom).toBe(40);
    });
    it('should move the pattern to positive coordinates', () => {
        const pattern = new Pattern();
        pattern.addStitchAbs(-10, -20, stitchTypes.normal);
        pattern.addStitchAbs(30, 40, stitchTypes.normal);
        pattern.calculateBoundingBox();
        pattern.moveToPositive();
        expect(pattern.left).toBe(0);
        expect(pattern.top).toBe(0);
        expect(pattern.right).toBe(40);
        expect(pattern.bottom).toBe(60);
        expect(pattern.stitches[0].x).toBe(0);
        expect(pattern.stitches[0].y).toBe(0);
        expect(pattern.stitches[1].x).toBe(40);
        expect(pattern.stitches[1].y).toBe(60);
    });
    it('should invert the pattern vertically', () => {
        const pattern = new Pattern();
        pattern.addStitchAbs(10, 20, stitchTypes.normal);
        pattern.addStitchAbs(30, 40, stitchTypes.normal);
        pattern.calculateBoundingBox();
        pattern.invertPatternVertical();
        expect(pattern.top).toBe(-40);
        expect(pattern.bottom).toBe(-20);
        expect(pattern.stitches[0].y).toBe(-20);
        expect(pattern.stitches[1].y).toBe(-40);
    });
    it('should fix the color count based on stitches', () => {
        const pattern = new Pattern();
        // Add a stitch with color index 2 (but no colors exist yet)
        pattern.currentColorIndex = 2;
        pattern.addStitchAbs(10, 20, stitchTypes.normal);
        // Fix the color count
        pattern.fixColorCount();
        // Should have 3 colors (indexes 0, 1, 2)
        expect(pattern.colors.length).toBe(3);
    });
});
//# sourceMappingURL=pattern.test.js.map