/**
 * Pattern - Implementation of the embroidery pattern class
 */
import { Stitch, Color, stitchTypes, IPattern } from '../types/embroidery';
export { Stitch, Color, stitchTypes };
/**
 * Pattern class for representing an embroidery design
 */
export declare class Pattern implements IPattern {
    colors: Color[];
    stitches: Stitch[];
    hoop: Record<string, any>;
    lastX: number;
    lastY: number;
    top: number;
    bottom: number;
    left: number;
    right: number;
    currentColorIndex: number;
    /**
     * Add a color to the pattern
     */
    addColor(color: Color): void;
    /**
     * Add a color specified by RGB values
     */
    addColorRgb(r: number, g: number, b: number, description?: string): void;
    /**
     * Add a stitch with absolute coordinates
     */
    addStitchAbs(x: number, y: number, flags: number, isAutoColorIndex?: boolean): void;
    /**
     * Add a stitch with relative coordinates
     */
    addStitchRel(dx: number, dy: number, flags: number, isAutoColorIndex?: boolean): void;
    /**
     * Calculate the bounding box of the pattern
     */
    calculateBoundingBox(): void;
    /**
     * Move the pattern to positive coordinates
     */
    moveToPositive(): void;
    /**
     * Invert the pattern vertically
     */
    invertPatternVertical(): void;
    /**
     * Add a random color
     */
    addColorRandom(): void;
    /**
     * Fix the color count based on stitches
     */
    fixColorCount(): void;
    /**
     * Calculate the width of the pattern
     */
    calculateWidth(): number;
    /**
     * Calculate the height of the pattern
     */
    calculateHeight(): number;
    /**
     * Draw the pattern on a canvas
     */
    drawShape(canvas: HTMLCanvasElement, lineWidth?: number, use3DEffect?: boolean, shadowBlur?: number, resolutionScale?: number): void;
    private drawStitchLayer;
    private adjustColor;
    private setStitchGradient;
}
