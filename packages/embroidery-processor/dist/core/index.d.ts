/**
 * Core Embroidery File Processing Module
 *
 * This module provides shared functionality for processing embroidery files
 * that can be used by both the frontend and batch processing scripts.
 *
 * The core module is designed to work in both browser and Node.js environments,
 * providing a consistent API for processing embroidery files regardless of the
 * execution environment.
 */
import { Pattern } from './pattern';
import { FormatReader, EmbroideryFileMetadata } from '../types/embroidery';
/**
 * Map of file extensions to their respective reader functions
 */
export declare const FILE_FORMAT_MAP: Record<string, FormatReader>;
/**
 * List of supported file formats (without the leading dot)
 */
export declare const SUPPORTED_FORMATS: string[];
/**
 * Check if a file format is supported
 * @param filename The filename to check
 * @returns True if the file format is supported
 */
export declare function isSupportedEmbroideryFormat(filename: string): boolean;
/**
 * Process an ArrayBuffer containing embroidery file data
 * @param arrayBuffer The ArrayBuffer containing the file data
 * @param filename The original filename (used for extension detection)
 * @returns A Promise that resolves to a Pattern object
 */
export declare function processEmbroideryBuffer(arrayBuffer: ArrayBuffer, filename: string): Promise<Pattern>;
/**
 * Extract metadata from a pattern
 * @param pattern The pattern to extract metadata from
 * @param filename The original filename
 * @param unit Optional unit for width/height (defaults to 'px')
 * @returns Metadata about the embroidery file
 */
export declare function extractMetadata(pattern: Pattern, filename: string, unit?: string): EmbroideryFileMetadata;
/**
 * Render a pattern on a canvas context
 * @param ctx The canvas context to render on
 * @param pattern The pattern to render
 * @param scale The scale factor to apply
 * @param lineWidth The line width to use
 * @param use3dEffect Whether to use a 3D effect
 */
export declare function renderPatternOnContext(ctx: any, // GenericCanvasContext
pattern: Pattern, scale?: number, lineWidth?: number, use3dEffect?: boolean): void;
/**
 * Calculate the scale factor to fit a pattern within a given width and height
 * @param pattern The pattern to scale
 * @param maxWidth The maximum width
 * @param maxHeight The maximum height
 * @param padding Optional padding (default: 10)
 * @returns The scale factor
 */
export declare function calculatePatternScale(pattern: Pattern, maxWidth: number, maxHeight: number, padding?: number): number;
