{"version": 3, "file": "dst-format.js", "sourceRoot": "", "sources": ["../../src/formats/dst-format.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;GAiBG;;AAoLH,0BAsKC;AAxVD,kDAA2D;AAC3D,IAAM,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,CAAA;AAClC,0CAAwC;AAGxC,kCAAkC;AAClC,IAAM,GAAG,GAAG,eAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAA;AAE5C,+BAA+B;AAC/B,IAAM,KAAK,GAAG,EAAE,CAAA,CAAC,0CAA0C;AAC3D,IAAM,MAAM,GAAG,GAAG,CAAA,CAAC,wBAAwB;AAE3C;;;;;;;;;;;;;;;;;;;GAmBG;AACH,SAAS,WAAW,CAAC,EAAU,EAAE,EAAU,EAAE,EAAU;IACrD,IAAI,UAAU,GAAG,CAAC,CAAA;IAElB,iDAAiD;IACjD,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC;QAChB,OAAO,wBAAW,CAAC,GAAG,CAAA;IACxB,CAAC;IAED,uEAAuE;IACvE,gDAAgD;IAChD,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;QACzB,OAAO,wBAAW,CAAC,IAAI,CAAA,CAAC,eAAe;IACzC,CAAC;IAED,gCAAgC;IAChC,oGAAoG;IACpG,8FAA8F;IAE9F,iCAAiC;IACjC,qFAAqF;IACrF,IAAM,cAAc,GAAG,EAAE,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAA;IAC1E,IAAM,qBAAqB,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,IAAI,CAAA,CAAC,sBAAsB;IAEzE,IAAI,cAAc,IAAI,qBAAqB,EAAE,CAAC;QAC5C,8DAA8D;QAC9D,OAAO,wBAAW,CAAC,IAAI,CAAA;IACzB,CAAC;IAED,kFAAkF;IAClF,IAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAA;IAE5D,IAAI,eAAe,EAAE,CAAC;QACpB,6DAA6D;QAC7D,IAAI,EAAE,GAAG,IAAI,EAAE,CAAC;YACd,qDAAqD;YACrD,UAAU,IAAI,wBAAW,CAAC,IAAI,CAAA;QAChC,CAAC;QAED,IAAI,EAAE,GAAG,IAAI,EAAE,CAAC;YACd,gDAAgD;YAChD,UAAU,IAAI,wBAAW,CAAC,IAAI,CAAA;QAChC,CAAC;IACH,CAAC;IAED,gEAAgE;IAChE,OAAO,UAAU,CAAA;AACnB,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAuCG;AACH,SAAS,eAAe,CACtB,EAAU,EACV,EAAU,EACV,EAAU;IAEV,IAAI,CAAC,GAAG,CAAC,CAAA;IACT,IAAI,CAAC,GAAG,CAAC,CAAA;IAET,8DAA8D;IAC9D,cAAc;IACd,IAAI,EAAE,GAAG,IAAI;QAAE,CAAC,IAAI,CAAC,CAAA,CAAC,MAAM;IAC5B,IAAI,EAAE,GAAG,IAAI;QAAE,CAAC,IAAI,CAAC,CAAA,CAAC,MAAM;IAC5B,IAAI,EAAE,GAAG,IAAI;QAAE,CAAC,IAAI,CAAC,CAAA,CAAC,MAAM;IAC5B,IAAI,EAAE,GAAG,IAAI;QAAE,CAAC,IAAI,CAAC,CAAA,CAAC,MAAM;IAC5B,IAAI,EAAE,GAAG,IAAI;QAAE,CAAC,IAAI,CAAC,CAAA,CAAC,MAAM;IAC5B,IAAI,EAAE,GAAG,IAAI;QAAE,CAAC,IAAI,CAAC,CAAA,CAAC,MAAM;IAC5B,IAAI,EAAE,GAAG,IAAI;QAAE,CAAC,IAAI,CAAC,CAAA,CAAC,MAAM;IAC5B,IAAI,EAAE,GAAG,IAAI;QAAE,CAAC,IAAI,CAAC,CAAA,CAAC,MAAM;IAE5B,cAAc;IACd,IAAI,EAAE,GAAG,IAAI;QAAE,CAAC,IAAI,CAAC,CAAA,CAAC,MAAM;IAC5B,IAAI,EAAE,GAAG,IAAI;QAAE,CAAC,IAAI,CAAC,CAAA,CAAC,MAAM;IAC5B,IAAI,EAAE,GAAG,IAAI;QAAE,CAAC,IAAI,EAAE,CAAA,CAAC,OAAO;IAC9B,IAAI,EAAE,GAAG,IAAI;QAAE,CAAC,IAAI,EAAE,CAAA,CAAC,OAAO;IAC9B,IAAI,EAAE,GAAG,IAAI;QAAE,CAAC,IAAI,CAAC,CAAA,CAAC,MAAM;IAC5B,IAAI,EAAE,GAAG,IAAI;QAAE,CAAC,IAAI,CAAC,CAAA,CAAC,MAAM;IAC5B,IAAI,EAAE,GAAG,IAAI;QAAE,CAAC,IAAI,EAAE,CAAA,CAAC,OAAO;IAC9B,IAAI,EAAE,GAAG,IAAI;QAAE,CAAC,IAAI,EAAE,CAAA,CAAC,OAAO;IAE9B,cAAc;IACd,+BAA+B;IAC/B,IAAI,EAAE,GAAG,IAAI;QAAE,CAAC,IAAI,EAAE,CAAA,CAAC,OAAO;IAC9B,IAAI,EAAE,GAAG,IAAI;QAAE,CAAC,IAAI,EAAE,CAAA,CAAC,OAAO;IAC9B,+BAA+B;IAC/B,IAAI,EAAE,GAAG,IAAI;QAAE,CAAC,IAAI,EAAE,CAAA,CAAC,OAAO;IAC9B,IAAI,EAAE,GAAG,IAAI;QAAE,CAAC,IAAI,EAAE,CAAA,CAAC,OAAO;IAE9B,uEAAuE;IACvE,kFAAkF;IAClF,IAAM,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;IAEnC,OAAO,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,KAAK,OAAA,EAAE,CAAA;AACxB,CAAC;AAED;;;;;;;;;;;;;GAaG;AACH,SAAgB,OAAO,CAAC,MAAmB,EAAE,OAAiB;;IAC5D,IAAM,MAAM,GAAG,IAAI,OAAO,CAAC,MAA2B,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAA;IAC/E,IAAI,CAAC;QACH,GAAG,CAAC,GAAG,CAAC,qBAAc,MAAM,CAAC,IAAI,CAAC,UAAU,WAAQ,CAAC,CAAA;QAErD,8BAA8B;QAC9B,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;YACjC,GAAG,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAA;YAErD,2CAA2C;YAC3C,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,GAAG,EAAE,EAAE,CAAC;gBAChC,wBAAwB,CAAC,OAAO,CAAC,CAAA;gBACjC,OAAM;YACR,CAAC;QACH,CAAC;QAED,0DAA0D;QAC1D,+EAA+E;QAC/E,mDAAmD;QACnD,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAEhB,uBAAuB;QACvB,IAAI,WAAW,GAAG,CAAC,CAAA;QACnB,IAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAA;QACxC,IAAI,oBAAoB,GAAG,CAAC,CAAA,CAAC,6CAA6C;QAE1E,sBAAsB;QACtB,GAAG,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAA;QACzC,OAAO,MAAM,CAAC,IAAI,EAAE,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;YACtC,IAAI,CAAC;gBACH,uCAAuC;gBACvC,IAAM,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAW,CAAA;gBACzC,IAAM,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAW,CAAA;gBACzC,IAAM,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAW,CAAA;gBAEzC,gDAAgD;gBAChD,iEAAiE;gBAC3D,IAAA,KAAkB,eAAe,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAA3C,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,KAAK,WAAgC,CAAA;gBAEnD,4CAA4C;gBAC5C,IAAI,KAAK,GAAG,wBAAW,CAAC,GAAG,EAAE,CAAC;oBAC5B,GAAG,CAAC,KAAK,CAAC,4CAAqC,WAAW,CAAE,EAAE,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAA;oBAChF,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,wBAAW,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;oBACjD,MAAK,CAAC,mCAAmC;gBAC3C,CAAC;gBAED,4DAA4D;gBAC5D,6DAA6D;gBAC7D,4CAA4C;gBAC5C,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC;oBAC3C,GAAG,CAAC,IAAI,CACN,8CAAuC,CAAC,kBAAQ,CAAC,sBAAY,KAAK,uBAAoB,EACtF,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAClB,CAAA;oBACD,6DAA6D;oBAC7D,SAAQ;gBACV,CAAC;gBAED,4CAA4C;gBAC5C,0GAA0G;gBAC1G,IAAI,UAAU,GAAG,KAAK,CAAA;gBAEtB,IAAI,KAAK,GAAG,wBAAW,CAAC,IAAI,EAAE,CAAC;oBAC7B,oBAAoB,EAAE,CAAA;oBAEtB,6CAA6C;oBAC7C,IAAI,oBAAoB,IAAI,CAAC,EAAE,CAAC;wBAC9B,UAAU,GAAG,wBAAW,CAAC,IAAI,CAAA;wBAC7B,GAAG,CAAC,KAAK,CACP,yDAAkD,WAAW,CAAE,EAC/D,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAClB,CAAA;wBACD,oBAAoB,GAAG,CAAC,CAAA,CAAC,gBAAgB;oBAC3C,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,oBAAoB,GAAG,CAAC,CAAA,CAAC,sCAAsC;gBACjE,CAAC;gBAED,6CAA6C;gBAC7C,IAAI,WAAW,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC;oBAC3B,GAAG,CAAC,KAAK,CACP,iBAAU,WAAW,kBAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,kBAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,OAAO,CAC1E,CAAC,CACF,qBAAW,UAAU,CAAE,EACxB,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAClB,CAAA;gBACH,CAAC;gBAED,6DAA6D;gBAC7D,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;gBAC1D,WAAW,EAAE,CAAA;YACf,CAAC;YAAC,OAAO,CAAU,EAAE,CAAC;gBACpB,wCAAwC;gBACxC,IAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,EAAE,CAAA;gBAC9B,GAAG,CAAC,KAAK,CAAC,iDAA0C,QAAQ,CAAE,EAAE,CAAC,CAAC,CAAA;gBAClE,8BAA8B;gBAC9B,IAAI,CAAC,YAAY,KAAK,EAAE,CAAC;oBACvB,GAAG,CAAC,KAAK,CAAC,yBAAkB,CAAC,CAAC,OAAO,CAAE,CAAC,CAAA;gBAC1C,CAAC;gBACD,sEAAsE;gBACtE,MAAK;YACP,CAAC;QACH,CAAC;QAED,kCAAkC;QAClC,IACE,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC;YAC3B,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,wBAAW,CAAC,GAAG,CAAC,EACxE,CAAC;YACD,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,wBAAW,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;QACnD,CAAC;QAED,uEAAuE;QACvE,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChC,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,CAAA;QACvC,CAAC;QAED,0DAA0D;QAC1D,OAAO,CAAC,qBAAqB,EAAE,CAAA;QAE/B,wDAAwD;QACxD,OAAO,CAAC,oBAAoB,EAAE,CAAA;QAE9B,4EAA4E;QAC5E,iDAAiD;QACjD,IAAM,UAAU,GACd,OAAO,OAAO,KAAK,WAAW,IAAI,CAAC,CAAA,MAAA,OAAO,CAAC,GAAG,0CAAE,QAAQ,MAAK,MAAM,KAAI,MAAA,OAAO,CAAC,GAAG,0CAAE,MAAM,CAAA,CAAC,CAAA;QAC7F,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,GAAG,KAAK,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACtF,GAAG,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAA;YACrE,sDAAsD;YACtD,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,EAAE,EAAE,wBAAW,CAAC,MAAM,CAAC,CAAA;YACjF,OAAO,CAAC,oBAAoB,EAAE,CAAA;QAChC,CAAC;QAED,kDAAkD;QAClD,IAAI,OAAO,CAAC,IAAI,KAAK,CAAC,IAAI,OAAO,CAAC,GAAG,KAAK,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3E,wDAAwD;YACxD,IAAI,IAAI,GAAG,MAAM,CAAC,SAAS,CAAA;YAC3B,IAAI,IAAI,GAAG,MAAM,CAAC,SAAS,CAAA;YAC3B,IAAI,IAAI,GAAG,MAAM,CAAC,SAAS,CAAA;YAC3B,IAAI,IAAI,GAAG,MAAM,CAAC,SAAS,CAAA;YAE3B,KAAqB,UAAgB,EAAhB,KAAA,OAAO,CAAC,QAAQ,EAAhB,cAAgB,EAAhB,IAAgB,EAAE,CAAC;gBAAnC,IAAM,MAAM,SAAA;gBACf,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAA;gBAC/B,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAA;gBAC/B,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAA;gBAC/B,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAA;YACjC,CAAC;YAED,gCAAgC;YAChC,IAAI,IAAI,KAAK,MAAM,CAAC,SAAS,EAAE,CAAC;gBAC9B,OAAO,CAAC,IAAI,GAAG,IAAI,CAAA;gBACnB,OAAO,CAAC,GAAG,GAAG,IAAI,CAAA;gBAClB,OAAO,CAAC,KAAK,GAAG,IAAI,CAAA;gBACpB,OAAO,CAAC,MAAM,GAAG,IAAI,CAAA;YACvB,CAAC;QACH,CAAC;QAED,GAAG,CAAC,GAAG,CAAC,iCAA0B,WAAW,cAAW,CAAC,CAAA;QACzD,GAAG,CAAC,KAAK,CACP,qCAA8B,OAAO,CAAC,IAAI,mBAAS,OAAO,CAAC,GAAG,qBAAW,OAAO,CAAC,KAAK,sBAAY,OAAO,CAAC,MAAM,CAAE,CACnH,CAAA;IACH,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,GAAG,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAA;QAClC,wBAAwB,CAAC,OAAO,CAAC,CAAA;IACnC,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,wBAAwB,CAAC,OAAiB;IACjD,0BAA0B;IAC1B,OAAO,CAAC,QAAQ,GAAG,EAAE,CAAA;IACrB,OAAO,CAAC,MAAM,GAAG,EAAE,CAAA;IAEnB,sBAAsB;IACtB,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,CAAA;IAErC,4BAA4B;IAC5B,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,wBAAW,CAAC,MAAM,CAAC,CAAA;IAC9C,OAAO,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,EAAE,wBAAW,CAAC,MAAM,CAAC,CAAA;IAC/C,OAAO,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,wBAAW,CAAC,MAAM,CAAC,CAAA;IAChD,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,EAAE,EAAE,wBAAW,CAAC,MAAM,CAAC,CAAA;IAC/C,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,wBAAW,CAAC,GAAG,CAAC,CAAA;IAE3C,GAAG,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAA;AAC9D,CAAC"}