/**
 * Browser-specific functionality for the embroidery processor
 */
import { isSupportedEmbroideryFormat, SUPPORTED_FORMATS } from './core';
import { EmbroideryProcessingResult } from './types/embroidery';
export { SUPPORTED_FORMATS, isSupportedEmbroideryFormat };
/**
 * Process an embroidery file and return metadata about it
 * @param file The file to process
 * @returns A promise that resolves to the metadata and a render function
 */
export declare function processEmbroideryFile(file: File): Promise<EmbroideryProcessingResult>;
