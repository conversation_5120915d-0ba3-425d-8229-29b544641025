{"version": 3, "file": "browser.js", "sourceRoot": "", "sources": ["../src/browser.ts"], "names": [], "mappings": "AAAA;;GAEG;AAGH,OAAO,EACL,uBAAuB,EACvB,2BAA2B,EAC3B,eAAe,EACf,iBAAiB,GAClB,MAAM,QAAQ,CAAA;AAEf,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAA;AAEvC,kCAAkC;AAClC,MAAM,GAAG,GAAG,MAAM,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAA;AAEhD,uDAAuD;AACvD,OAAO,EAAE,iBAAiB,EAAE,2BAA2B,EAAE,CAAA;AAEzD;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,qBAAqB,CAAC,IAAU;IACpD,IAAI,CAAC;QACH,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAA;QACrE,CAAC;QAED,kCAAkC;QAClC,MAAM,WAAW,GAAG,MAAM,IAAI,OAAO,CAAc,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrE,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAA;YAC/B,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE;gBACpB,IAAI,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;oBAClC,MAAM,CAAC,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAA;oBACxC,OAAM;gBACR,CAAC;gBACD,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,MAAqB,CAAC,CAAA;YACzC,CAAC,CAAA;YACD,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAA;YAC9D,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA;QAChC,CAAC,CAAC,CAAA;QAEF,GAAG,CAAC,GAAG,CAAC,oBAAoB,IAAI,CAAC,IAAI,WAAW,WAAW,CAAC,UAAU,QAAQ,CAAC,CAAA;QAE/E,6CAA6C;QAC7C,MAAM,OAAO,GAAG,MAAM,uBAAuB,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QAErE,6CAA6C;QAC7C,OAAO,CAAC,cAAc,EAAE,CAAA;QAExB,mBAAmB;QACnB,MAAM,QAAQ,GAAG,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QAEpD,OAAO;YACL,QAAQ;YACR,OAAO;SACR,CAAA;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,GAAG,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAA;QACpD,MAAM,KAAK,CAAA;IACb,CAAC;AACH,CAAC"}