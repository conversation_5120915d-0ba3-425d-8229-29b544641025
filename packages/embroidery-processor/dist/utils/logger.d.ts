/**
 * Logger utility for embroidery processor
 */
export declare const logger: {
    debug: (message: string, ...args: any[]) => void;
    log: (message: string, ...args: any[]) => void;
    warn: (message: string, ...args: any[]) => void;
    error: (message: string, error?: unknown) => void;
    createFormatLogger: (formatName: string) => {
        debug: (message: string, offset?: number, ...args: any[]) => void;
        log: (message: string, offset?: number, ...args: any[]) => void;
        warn: (message: string, offset?: number, ...args: any[]) => void;
        error: (message: string, error?: unknown, offset?: number) => void;
    };
};
