/**
 * Logger utility for embroidery processor
 */
// Create a simple logger for the embroidery processor
export const logger = {
    debug: (message, ...args) => {
        console.debug(`[EmbroideryProcessor] ${message}`, ...args);
    },
    log: (message, ...args) => {
        console.log(`[EmbroideryProcessor] ${message}`, ...args);
    },
    warn: (message, ...args) => {
        console.warn(`[EmbroideryProcessor] ${message}`, ...args);
    },
    error: (message, error) => {
        if (error) {
            console.error(`[EmbroideryProcessor] ${message}`, error);
        }
        else {
            console.error(`[EmbroideryProcessor] ${message}`);
        }
    },
    createFormatLogger: (formatName) => {
        const prefix = `EmbroideryProcessor:${formatName}`;
        return {
            debug: (message, offset, ...args) => {
                const offsetStr = offset !== undefined ? `[0x${offset.toString(16).padStart(4, '0')}] ` : '';
                console.debug(`[${prefix}] ${offsetStr}${message}`, ...args);
            },
            log: (message, offset, ...args) => {
                const offsetStr = offset !== undefined ? `[0x${offset.toString(16).padStart(4, '0')}] ` : '';
                console.log(`[${prefix}] ${offsetStr}${message}`, ...args);
            },
            warn: (message, offset, ...args) => {
                const offsetStr = offset !== undefined ? `[0x${offset.toString(16).padStart(4, '0')}] ` : '';
                console.warn(`[${prefix}] ${offsetStr}${message}`, ...args);
            },
            error: (message, error, offset) => {
                const offsetStr = offset !== undefined ? `[0x${offset.toString(16).padStart(4, '0')}] ` : '';
                if (error) {
                    console.error(`[${prefix}] ${offsetStr}${message}`, error);
                }
                else {
                    console.error(`[${prefix}] ${offsetStr}${message}`);
                }
            },
        };
    },
};
//# sourceMappingURL=logger.js.map