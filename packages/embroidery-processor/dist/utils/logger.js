"use strict";
/**
 * Logger utility for embroidery processor
 */
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = void 0;
// Create a simple logger for the embroidery processor
exports.logger = {
    debug: function (message) {
        var args = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args[_i - 1] = arguments[_i];
        }
        console.debug.apply(console, __spreadArray(["[EmbroideryProcessor] ".concat(message)], args, false));
    },
    log: function (message) {
        var args = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args[_i - 1] = arguments[_i];
        }
        console.log.apply(console, __spreadArray(["[EmbroideryProcessor] ".concat(message)], args, false));
    },
    warn: function (message) {
        var args = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args[_i - 1] = arguments[_i];
        }
        console.warn.apply(console, __spreadArray(["[EmbroideryProcessor] ".concat(message)], args, false));
    },
    error: function (message, error) {
        if (error) {
            console.error("[EmbroideryProcessor] ".concat(message), error);
        }
        else {
            console.error("[EmbroideryProcessor] ".concat(message));
        }
    },
    createFormatLogger: function (formatName) {
        var prefix = "EmbroideryProcessor:".concat(formatName);
        return {
            debug: function (message, offset) {
                var args = [];
                for (var _i = 2; _i < arguments.length; _i++) {
                    args[_i - 2] = arguments[_i];
                }
                var offsetStr = offset !== undefined ? "[0x".concat(offset.toString(16).padStart(4, '0'), "] ") : '';
                console.debug.apply(console, __spreadArray(["[".concat(prefix, "] ").concat(offsetStr).concat(message)], args, false));
            },
            log: function (message, offset) {
                var args = [];
                for (var _i = 2; _i < arguments.length; _i++) {
                    args[_i - 2] = arguments[_i];
                }
                var offsetStr = offset !== undefined ? "[0x".concat(offset.toString(16).padStart(4, '0'), "] ") : '';
                console.log.apply(console, __spreadArray(["[".concat(prefix, "] ").concat(offsetStr).concat(message)], args, false));
            },
            warn: function (message, offset) {
                var args = [];
                for (var _i = 2; _i < arguments.length; _i++) {
                    args[_i - 2] = arguments[_i];
                }
                var offsetStr = offset !== undefined ? "[0x".concat(offset.toString(16).padStart(4, '0'), "] ") : '';
                console.warn.apply(console, __spreadArray(["[".concat(prefix, "] ").concat(offsetStr).concat(message)], args, false));
            },
            error: function (message, error, offset) {
                var offsetStr = offset !== undefined ? "[0x".concat(offset.toString(16).padStart(4, '0'), "] ") : '';
                if (error) {
                    console.error("[".concat(prefix, "] ").concat(offsetStr).concat(message), error);
                }
                else {
                    console.error("[".concat(prefix, "] ").concat(offsetStr).concat(message));
                }
            },
        };
    },
};
