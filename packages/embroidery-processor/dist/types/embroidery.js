"use strict";
/**
 * Embroidery types for the StitchEstimate project
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Color = exports.Stitch = exports.stitchTypes = void 0;
/**
 * Stitch types enum
 */
exports.stitchTypes = {
    normal: 0,
    jump: 1,
    trim: 2,
    stop: 4,
    end: 8,
};
/**
 * Stitch class for representing a single stitch in an embroidery pattern
 */
var Stitch = /** @class */ (function () {
    function Stitch(x, y, flags, color) {
        this.x = x;
        this.y = y;
        this.flags = flags;
        this.color = color;
    }
    return Stitch;
}());
exports.Stitch = Stitch;
/**
 * Color class for representing a thread color in an embroidery pattern
 */
var Color = /** @class */ (function () {
    function Color(r, g, b, description) {
        if (description === void 0) { description = ''; }
        this.r = r;
        this.g = g;
        this.b = b;
        this.description = description;
    }
    return Color;
}());
exports.Color = Color;
