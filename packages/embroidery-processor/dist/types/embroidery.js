/**
 * Embroidery types for the StitchEstimate project
 */
/**
 * <PERSON><PERSON> types enum
 */
export const stitchTypes = {
    normal: 0,
    jump: 1,
    trim: 2,
    stop: 4,
    end: 8,
};
/**
 * Stitch class for representing a single stitch in an embroidery pattern
 */
export class Stitch {
    x;
    y;
    flags;
    color;
    constructor(x, y, flags, color) {
        this.x = x;
        this.y = y;
        this.flags = flags;
        this.color = color;
    }
}
/**
 * Color class for representing a thread color in an embroidery pattern
 */
export class Color {
    r;
    g;
    b;
    description;
    constructor(r, g, b, description = '') {
        this.r = r;
        this.g = g;
        this.b = b;
        this.description = description;
    }
}
//# sourceMappingURL=embroidery.js.map