{"name": "@stitchestimate/embroidery-processor", "version": "0.1.0", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "type": "module", "scripts": {"build": "tsc", "dev": "tsc --watch", "typecheck": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest watch", "clean": "rm -rf dist"}, "dependencies": {"jbinary": "^2.1.5", "canvas": "^3.1.0"}, "devDependencies": {"@types/jbinary": "^2.1.4", "@types/node": "^22.15.13", "typescript": "^5.7.2", "vitest": "^1.6.1"}}