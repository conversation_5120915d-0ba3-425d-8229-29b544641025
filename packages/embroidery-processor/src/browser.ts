/**
 * Browser-specific functionality for the embroidery processor
 */

import { Pattern } from './core/pattern'
import {
  processEmbroideryBuffer,
  isSupportedEmbroideryFormat,
  extractMetadata,
  SUPPORTED_FORMATS,
} from './core'
import { EmbroideryProcessingResult, EmbroideryFileMetadata } from './types/embroidery'
import { logger } from './utils/logger'

// Create a module-specific logger
const log = logger.createFormatLogger('browser')

// Re-export SUPPORTED_FORMATS for use in other modules
export { SUPPORTED_FORMATS, isSupportedEmbroideryFormat }

/**
 * Process an embroidery file and return metadata about it
 * @param file The file to process
 * @returns A promise that resolves to the metadata and a render function
 */
export async function processEmbroideryFile(file: File): Promise<EmbroideryProcessingResult> {
  try {
    if (typeof window === 'undefined') {
      throw new Error('File processing is only available in the browser')
    }

    // Read the file as an ArrayBuffer
    const arrayBuffer = await new Promise<ArrayBuffer>((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => {
        if (!e.target || !e.target.result) {
          reject(new Error('Failed to read file'))
          return
        }
        resolve(e.target.result as ArrayBuffer)
      }
      reader.onerror = () => reject(new Error('Error reading file'))
      reader.readAsArrayBuffer(file)
    })

    log.log(`Processing file: ${file.name}, size: ${arrayBuffer.byteLength} bytes`)

    // Process the buffer using our core function
    const pattern = await processEmbroideryBuffer(arrayBuffer, file.name)

    // Move to positive coordinates for rendering
    pattern.moveToPositive()

    // Extract metadata
    const metadata = extractMetadata(pattern, file.name)

    return {
      metadata,
      pattern,
    }
  } catch (error) {
    log.error('Error processing embroidery file', error)
    throw error
  }
}
