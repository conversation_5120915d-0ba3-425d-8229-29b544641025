/**
 * Embroidery Processor Package
 *
 * This package provides functionality for processing embroidery files
 * that can be used by both the frontend and batch processing scripts.
 *
 * The core module is designed to work in both browser and Node.js environments,
 * providing a consistent API for processing embroidery files regardless of the
 * execution environment.
 */

// Export core functionality
export * from './core'

// Export Pattern class and related types
export * from './core/pattern'

// Export types
export * from './types'

// Export format readers
export * from './formats'

// Export browser-specific functionality
export * from './browser'
