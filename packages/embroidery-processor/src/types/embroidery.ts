/**
 * Embroidery types for the StitchEstimate project
 */

/**
 * <PERSON><PERSON> types enum
 */
export const stitchTypes = {
  normal: 0,
  jump: 1,
  trim: 2,
  stop: 4,
  end: 8,
};

/**
 * Stitch class for representing a single stitch in an embroidery pattern
 */
export class Stitch {
  constructor(
    public x: number,
    public y: number,
    public flags: number,
    public color: number,
  ) {}
}

/**
 * Color class for representing a thread color in an embroidery pattern
 */
export class Color {
  constructor(
    public r: number,
    public g: number,
    public b: number,
    public description: string = '',
  ) {}
}

/**
 * Pattern class for representing an embroidery design
 */
export interface IPattern {
  colors: Color[];
  stitches: Stitch[];
  hoop: Record<string, any>;
  lastX: number;
  lastY: number;
  top: number;
  bottom: number;
  left: number;
  right: number;
  currentColorIndex: number;
  
  addColor(color: Color): void;
  addColorRgb(r: number, g: number, b: number, description?: string): void;
  addStitchAbs(x: number, y: number, flags: number, isAutoColorIndex?: boolean): void;
  addStitchRel(dx: number, dy: number, flags: number, isAutoColorIndex?: boolean): void;
  calculateBoundingBox(): void;
  moveToPositive(): void;
  invertPatternVertical(): void;
  fixColorCount(): void;
}

/**
 * Metadata about an embroidery file
 */
export interface EmbroideryFileMetadata {
  /** Format of the embroidery file */
  format: string;
  /** Width of the design in pixels or units */
  width: number | { value: number; unit: string };
  /** Height of the design in pixels or units */
  height: number | { value: number; unit: string };
  /** Number of stitches in the design */
  stitchCount: number;
  /** Number of colors in the design */
  colorCount: number;
  /** Original filename */
  filename: string;
}

/**
 * Function type for reading embroidery files
 */
export type FormatReader = (buffer: ArrayBuffer, pattern: IPattern) => void;

/**
 * File type set for embroidery formats
 */
export interface FileTypeSet {
  /** Display name of the format */
  name: string;
  /** File extension without dot */
  extension: string;
  /** MIME type */
  mimeType: string;
  /** Reader function */
  read: FormatReader;
}

/**
 * Return type for the processEmbroideryFile function
 */
export interface EmbroideryProcessingResult {
  /** Metadata about the embroidery file */
  metadata: EmbroideryFileMetadata;
  /** The processed pattern object */
  pattern: IPattern;
}
