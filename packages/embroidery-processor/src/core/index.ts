/**
 * Core Embroidery File Processing Module
 *
 * This module provides shared functionality for processing embroidery files
 * that can be used by both the frontend and batch processing scripts.
 *
 * The core module is designed to work in both browser and Node.js environments,
 * providing a consistent API for processing embroidery files regardless of the
 * execution environment.
 */

import { Pattern } from './pattern'
import { FormatReader, EmbroideryFileMetadata } from '../types/embroidery'
import { dstRead, pesRead, jefRead, expRead, vp3Read } from '../formats'
import { logger } from '../utils/logger'

// Create a module-specific logger
const log = logger.createFormatLogger('core')

/**
 * Map of file extensions to their respective reader functions
 */
export const FILE_FORMAT_MAP: Record<string, FormatReader> = {
  '.dst': dstRead,
  dst: dstRead,
  '.pes': pesRead,
  pes: pesRead,
  '.pec': pesRead, // PEC is embedded in PES, so we use the same parser
  pec: pesRead, // PEC is embedded in PES, so we use the same parser
  '.jef': jefRead,
  jef: jefRead,
  '.exp': expRead,
  exp: expRead,
  '.vp3': vp3Read,
  vp3: vp3Read,
}

/**
 * List of supported file formats (without the leading dot)
 */
export const SUPPORTED_FORMATS = Object.keys(FILE_FORMAT_MAP)
  .filter((ext) => ext.startsWith('.')) // Only take the ones with dots
  .map((ext) => ext.substring(1)) // Remove the dot

/**
 * Check if a file format is supported
 * @param filename The filename to check
 * @returns True if the file format is supported
 */
export function isSupportedEmbroideryFormat(filename: string): boolean {
  const dotPosition = filename.lastIndexOf('.')
  const extension = dotPosition >= 0 ? filename.substring(dotPosition).toLowerCase() : ''
  return extension in FILE_FORMAT_MAP
}

/**
 * Process an ArrayBuffer containing embroidery file data
 * @param arrayBuffer The ArrayBuffer containing the file data
 * @param filename The original filename (used for extension detection)
 * @returns A Promise that resolves to a Pattern object
 */
export async function processEmbroideryBuffer(
  arrayBuffer: ArrayBuffer,
  filename: string,
): Promise<Pattern> {
  // Get the file extension
  const dotPosition = filename.lastIndexOf('.')
  const extension = dotPosition >= 0 ? filename.substring(dotPosition).toLowerCase() : ''

  // Check if this is a supported format
  if (!(extension in FILE_FORMAT_MAP)) {
    throw new Error(`Unsupported file format: ${extension}`)
  }

  // Create a new pattern
  const pattern = new Pattern()

  try {
    // Get the reader function for this format
    const reader = FILE_FORMAT_MAP[extension]

    // Read the file
    reader(arrayBuffer, pattern)

    // Calculate the bounding box
    pattern.calculateBoundingBox()

    return pattern
  } catch (error) {
    log.error(`Error processing embroidery file: ${filename}`, error)
    throw error
  }
}

/**
 * Extract metadata from a pattern
 * @param pattern The pattern to extract metadata from
 * @param filename The original filename
 * @param unit Optional unit for width/height (defaults to 'px')
 * @returns Metadata about the embroidery file
 */
export function extractMetadata(
  pattern: Pattern,
  filename: string,
  unit: string = 'px',
): EmbroideryFileMetadata {
  // Get the file extension
  const dotPosition = filename.lastIndexOf('.')
  const extension = dotPosition >= 0 ? filename.substring(dotPosition).toLowerCase() : ''
  const format = extension.startsWith('.') ? extension.substring(1) : extension

  // Calculate dimensions
  const width = pattern.right - pattern.left
  const height = pattern.bottom - pattern.top

  // Create width/height objects if unit is provided
  const widthValue = unit ? { value: width, unit } : width
  const heightValue = unit ? { value: height, unit } : height

  return {
    format,
    width: widthValue,
    height: heightValue,
    stitchCount: pattern.stitches.length,
    colorCount: pattern.colors.length,
    filename,
  }
}

/**
 * Render a pattern on a canvas context
 * @param ctx The canvas context to render on
 * @param pattern The pattern to render
 * @param scale The scale factor to apply
 * @param lineWidth The line width to use
 * @param use3dEffect Whether to use a 3D effect
 */
export function renderPatternOnContext(
  ctx: any, // GenericCanvasContext
  pattern: Pattern,
  scale: number = 1.0,
  lineWidth: number = 1.0,
  use3dEffect: boolean = false,
): void {
  const stitchCount = pattern.stitches.length
  if (stitchCount === 0) return

  // Save the current context state
  ctx.save()

  // Set line properties
  ctx.lineWidth = lineWidth
  ctx.lineCap = 'round'
  ctx.lineJoin = 'round'

  // Apply scaling
  ctx.scale(scale, scale)

  let lastX = pattern.stitches[0].x
  let lastY = pattern.stitches[0].y
  let lastColor = pattern.stitches[0].color
  let currentColor = pattern.colors[lastColor]

  // Set initial color
  ctx.strokeStyle = `rgb(${currentColor.r}, ${currentColor.g}, ${currentColor.b})`

  // Apply 3D effect if requested
  if (use3dEffect) {
    ctx.shadowBlur = 2
    ctx.shadowColor = 'rgba(0, 0, 0, 0.5)'
    ctx.shadowOffsetX = 0.5
    ctx.shadowOffsetY = 0.5
  }

  // Start the path
  ctx.beginPath()
  ctx.moveTo(lastX, lastY)

  // Draw the stitches
  for (let i = 1; i < stitchCount; i++) {
    const stitch = pattern.stitches[i]
    const { x, y, flags, color } = stitch

    // Check if this is a color change or special stitch
    if (color !== lastColor || (flags & 0xf) !== 0) {
      // Stroke the current path
      ctx.stroke()

      // Start a new path
      ctx.beginPath()
      ctx.moveTo(lastX, lastY)

      // Update color if needed
      if (color !== lastColor) {
        lastColor = color
        currentColor = pattern.colors[color]
        ctx.strokeStyle = `rgb(${currentColor.r}, ${currentColor.g}, ${currentColor.b})`
      }

      // Skip drawing for jump, trim, stop, or end stitches
      if ((flags & 0xf) !== 0) {
        lastX = x
        lastY = y
        ctx.moveTo(x, y)
        continue
      }
    }

    // Draw line to the current stitch
    ctx.lineTo(x, y)
    lastX = x
    lastY = y
  }

  // Stroke any remaining path
  ctx.stroke()

  // Restore the context state
  ctx.restore()
}

/**
 * Calculate the scale factor to fit a pattern within a given width and height
 * @param pattern The pattern to scale
 * @param maxWidth The maximum width
 * @param maxHeight The maximum height
 * @param padding Optional padding (default: 10)
 * @returns The scale factor
 */
export function calculatePatternScale(
  pattern: Pattern,
  maxWidth: number,
  maxHeight: number,
  padding: number = 10,
): number {
  const patternWidth = pattern.right - pattern.left
  const patternHeight = pattern.bottom - pattern.top

  if (patternWidth === 0 || patternHeight === 0) {
    return 1.0
  }

  const availableWidth = maxWidth - padding * 2
  const availableHeight = maxHeight - padding * 2

  const widthScale = availableWidth / patternWidth
  const heightScale = availableHeight / patternHeight

  // Return the smaller scale to ensure the pattern fits
  return Math.min(widthScale, heightScale)
}
