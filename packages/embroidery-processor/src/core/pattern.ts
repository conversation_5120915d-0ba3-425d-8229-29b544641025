/**
 * Pattern - Implementation of the embroidery pattern class
 */

import { Stitch, Color, stitchTypes, IPattern } from '../types/embroidery'

export { Stitch, Color, stitchTypes }

/**
 * Pattern class for representing an embroidery design
 */
export class <PERSON><PERSON> implements IPattern {
  public colors: Color[] = []
  public stitches: Stitch[] = []
  public hoop: Record<string, any> = {}
  public lastX: number = 0
  public lastY: number = 0
  public top: number = 0
  public bottom: number = 0
  public left: number = 0
  public right: number = 0
  public currentColorIndex: number = 0

  /**
   * Add a color to the pattern
   */
  public addColor(color: Color): void {
    this.colors[this.colors.length] = color
  }

  /**
   * Add a color specified by RGB values
   */
  public addColorRgb(r: number, g: number, b: number, description: string = ''): void {
    this.colors[this.colors.length] = new Color(r, g, b, description)
  }

  /**
   * Add a stitch with absolute coordinates
   */
  public addStitchAbs(
    x: number,
    y: number,
    flags: number,
    isAutoColorIndex: boolean = false,
  ): void {
    if ((flags & stitchTypes.end) === stitchTypes.end) {
      this.calculateBoundingBox()
      this.fixColorCount()
    }

    if ((flags & stitchTypes.stop) === stitchTypes.stop && this.stitches.length === 0) {
      return
    }

    if ((flags & stitchTypes.stop) === stitchTypes.stop && isAutoColorIndex) {
      this.currentColorIndex += 1
    }

    this.stitches[this.stitches.length] = new Stitch(x, y, flags, this.currentColorIndex)
  }

  /**
   * Add a stitch with relative coordinates
   */
  public addStitchRel(
    dx: number,
    dy: number,
    flags: number,
    isAutoColorIndex: boolean = false,
  ): void {
    if (this.stitches.length !== 0) {
      const nx = this.lastX + dx
      const ny = this.lastY + dy
      this.lastX = nx
      this.lastY = ny
      this.addStitchAbs(nx, ny, flags, isAutoColorIndex)
    } else {
      // For the first stitch, use the coordinates as absolute
      this.addStitchAbs(dx, dy, flags, isAutoColorIndex)
      // Update lastX and lastY for the first stitch to ensure proper relative positioning
      this.lastX = dx
      this.lastY = dy
    }
  }

  /**
   * Calculate the bounding box of the pattern
   */
  public calculateBoundingBox(): void {
    const stitchCount = this.stitches.length
    if (stitchCount === 0) return

    let minX = Number.MAX_VALUE
    let minY = Number.MAX_VALUE
    let maxX = Number.MIN_VALUE
    let maxY = Number.MIN_VALUE

    for (let i = 0; i < stitchCount; i++) {
      const { x, y } = this.stitches[i]
      minX = Math.min(minX, x)
      minY = Math.min(minY, y)
      maxX = Math.max(maxX, x)
      maxY = Math.max(maxY, y)
    }

    this.left = minX
    this.top = minY
    this.right = maxX
    this.bottom = maxY
  }

  /**
   * Move the pattern to positive coordinates
   */
  public moveToPositive(): void {
    const stitchCount = this.stitches.length

    for (let i = 0; i < stitchCount; i++) {
      this.stitches[i].x -= this.left
      this.stitches[i].y -= this.top
    }

    this.right -= this.left
    this.left = 0
    this.bottom -= this.top
    this.top = 0
  }

  /**
   * Invert the pattern vertically
   */
  public invertPatternVertical(): void {
    const temp = -this.top
    const stitchCount = this.stitches.length

    for (let i = 0; i < stitchCount; i++) {
      this.stitches[i].y = -this.stitches[i].y
    }

    this.top = -this.bottom
    this.bottom = temp
  }

  /**
   * Add a random color
   */
  public addColorRandom(): void {
    this.colors[this.colors.length] = new Color(
      Math.round(Math.random() * 256),
      Math.round(Math.random() * 256),
      Math.round(Math.random() * 256),
      'random',
    )
  }

  /**
   * Fix the color count based on stitches
   */
  public fixColorCount(): void {
    let maxColorIndex = 0
    const stitchCount = this.stitches.length

    for (let i = 0; i < stitchCount; i++) {
      maxColorIndex = Math.max(maxColorIndex, this.stitches[i].color)
    }

    while (this.colors.length <= maxColorIndex) {
      this.addColorRandom()
    }

    this.colors.splice(maxColorIndex + 1, this.colors.length - maxColorIndex - 1)
  }

  /**
   * Calculate the width of the pattern
   */
  public calculateWidth(): number {
    return Math.max(1, this.right - this.left)
  }

  /**
   * Calculate the height of the pattern
   */
  public calculateHeight(): number {
    return Math.max(1, this.bottom - this.top)
  }

  /**
   * Draw the pattern on a canvas
   */
  public drawShape(
    canvas: HTMLCanvasElement,
    lineWidth: number = 1,
    use3DEffect: boolean = false,
    shadowBlur: number = 4,
    resolutionScale: number = 1,
  ): void {
    // Get the original canvas dimensions from the element's attributes
    const canvasWidth = canvas.width
    const canvasHeight = canvas.height

    // Store the pattern dimensions
    const patternWidth = this.right
    const patternHeight = this.bottom

    // Calculate the aspect ratio of the pattern
    const patternRatio = patternWidth / patternHeight

    // Safety check: limit resolution scaling to prevent exceeding browser limits
    // Most browsers have a max canvas size of around 16,384 pixels in any dimension
    const MAX_CANVAS_DIMENSION = 8192 // Set a safe limit below most browser maximums
    const safeResolutionScale = Math.min(
      resolutionScale,
      MAX_CANVAS_DIMENSION / canvasWidth,
      MAX_CANVAS_DIMENSION / canvasHeight,
    )

    // If we had to adjust the scale, log a warning
    if (safeResolutionScale < resolutionScale) {
      console.warn(
        `Resolution scale reduced from ${resolutionScale} to ${safeResolutionScale} to prevent exceeding maximum canvas dimensions`,
      )
    }

    // Apply resolution scaling while preserving the original canvas dimensions
    canvas.width = canvasWidth * safeResolutionScale
    canvas.height = canvasHeight * safeResolutionScale

    // Don't modify the CSS dimensions - React is already handling this

    if (canvas.getContext) {
      const ctx = canvas.getContext('2d')
      if (!ctx) return

      // Scale according to resolution scale
      ctx.scale(safeResolutionScale, safeResolutionScale)

      // Calculate the fit scaling to maintain aspect ratio
      const canvasRatio = canvasWidth / canvasHeight
      let scale: number
      let offsetX = 0
      let offsetY = 0

      if (patternRatio > canvasRatio) {
        // Pattern is wider than canvas
        scale = canvasWidth / patternWidth
        offsetY = (canvasHeight - patternHeight * scale) / 2
      } else {
        // Pattern is taller than canvas
        scale = canvasHeight / patternHeight
        offsetX = (canvasWidth - patternWidth * scale) / 2
      }

      // Apply the fit scaling and centering
      ctx.translate(offsetX, offsetY)
      ctx.scale(scale, scale)

      ctx.lineJoin = 'round'
      ctx.lineCap = 'round'

      if (this.stitches.length === 0) return

      if (use3DEffect) {
        this.drawStitchLayer(ctx, lineWidth * 1.7, true, {
          shadowBlur: shadowBlur * 1.8,
          shadowColor: 'rgba(0,0,0,0.8)',
          shadowOffsetX: lineWidth * 0.5,
          shadowOffsetY: lineWidth * 0.5,
          colorAdjust: -90,
        })

        this.drawStitchLayer(ctx, lineWidth * 1.3, false, {
          shadowBlur: 0,
          shadowColor: 'transparent',
          shadowOffsetX: 0,
          shadowOffsetY: 0,
          colorAdjust: -50,
        })

        this.drawStitchLayer(ctx, lineWidth, false, {
          shadowBlur: 0,
          shadowColor: 'transparent',
          shadowOffsetX: 0,
          shadowOffsetY: 0,
          colorAdjust: 0,
          useHighlight: true,
        })
      } else {
        ctx.lineWidth = lineWidth
        ctx.beginPath()
        const color = this.colors[this.stitches[0].color]
        ctx.strokeStyle = `rgb(${color.r},${color.g},${color.b})`

        for (let i = 0; i < this.stitches.length; i++) {
          const currentStitch = this.stitches[i]

          if (
            currentStitch.flags === stitchTypes.jump ||
            currentStitch.flags === stitchTypes.trim ||
            currentStitch.flags === stitchTypes.stop
          ) {
            ctx.stroke()
            const color = this.colors[currentStitch.color]

            ctx.beginPath()
            ctx.strokeStyle = `rgb(${color.r},${color.g},${color.b})`

            ctx.moveTo(currentStitch.x, currentStitch.y)
          }
          ctx.lineTo(currentStitch.x, currentStitch.y)
        }

        ctx.stroke()
      }
    } else {
      if (typeof window !== 'undefined' && window.alert) {
        window.alert('You need Safari or Firefox 1.5+ to see this demo.')
      }
    }
  }

  private drawStitchLayer(
    ctx: CanvasRenderingContext2D,
    lineWidth: number,
    isBaseShadow: boolean,
    settings: {
      shadowBlur: number
      shadowColor: string
      shadowOffsetX: number
      shadowOffsetY: number
      colorAdjust: number
      useHighlight?: boolean
    },
  ): void {
    ctx.save()
    ctx.lineWidth = lineWidth

    ctx.shadowBlur = settings.shadowBlur
    ctx.shadowColor = settings.shadowColor
    ctx.shadowOffsetX = settings.shadowOffsetX
    ctx.shadowOffsetY = settings.shadowOffsetY

    ctx.beginPath()
    const initialColor = this.colors[this.stitches[0].color]

    if (settings.useHighlight) {
      this.setStitchGradient(ctx, initialColor, lineWidth)
    } else {
      const adjustedColor = this.adjustColor(initialColor, settings.colorAdjust)
      ctx.strokeStyle = `rgb(${adjustedColor.r},${adjustedColor.g},${adjustedColor.b})`
    }

    for (let i = 0; i < this.stitches.length; i++) {
      const currentStitch = this.stitches[i]

      if (
        currentStitch.flags === stitchTypes.jump ||
        currentStitch.flags === stitchTypes.trim ||
        currentStitch.flags === stitchTypes.stop
      ) {
        ctx.stroke()
        const color = this.colors[currentStitch.color]

        ctx.beginPath()
        if (settings.useHighlight) {
          this.setStitchGradient(ctx, color, lineWidth)
        } else {
          const adjustedColor = this.adjustColor(color, settings.colorAdjust)
          ctx.strokeStyle = `rgb(${adjustedColor.r},${adjustedColor.g},${adjustedColor.b})`
        }

        ctx.moveTo(currentStitch.x, currentStitch.y)
      }
      ctx.lineTo(currentStitch.x, currentStitch.y)
    }

    ctx.stroke()
    ctx.restore()
  }

  private adjustColor(color: Color, amount: number): { r: number; g: number; b: number } {
    return {
      r: Math.max(0, Math.min(255, color.r + amount)),
      g: Math.max(0, Math.min(255, color.g + amount)),
      b: Math.max(0, Math.min(255, color.b + amount)),
    }
  }

  private setStitchGradient(ctx: CanvasRenderingContext2D, color: Color, lineWidth: number): void {
    const gradient = ctx.createLinearGradient(0, 0, lineWidth, lineWidth)

    gradient.addColorStop(
      0,
      `rgb(${Math.min(255, color.r + 70)},${Math.min(255, color.g + 70)},${Math.min(
        255,
        color.b + 70,
      )})`,
    )

    gradient.addColorStop(
      0.3,
      `rgb(${Math.min(255, color.r + 15)},${Math.min(255, color.g + 15)},${Math.min(
        255,
        color.b + 15,
      )})`,
    )

    gradient.addColorStop(0.5, `rgb(${color.r},${color.g},${color.b})`)

    gradient.addColorStop(
      0.7,
      `rgb(${Math.max(0, color.r - 30)},${Math.max(0, color.g - 30)},${Math.max(0, color.b - 30)})`,
    )

    gradient.addColorStop(
      1,
      `rgb(${Math.max(0, color.r - 100)},${Math.max(0, color.g - 100)},${Math.max(
        0,
        color.b - 100,
      )})`,
    )

    ctx.strokeStyle = gradient

    if (Math.random() > 0.7) {
      ctx.setLineDash([lineWidth * 0.8, lineWidth * 0.2])
    } else {
      ctx.setLineDash([])
    }
  }
}
