{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "baseUrl": ".", "noEmit": false, "declaration": true, "sourceMap": true, "module": "esnext", "target": "es2020", "moduleResolution": "bundler", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "paths": {"@/*": ["./src/*"]}}, "include": ["src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules", "dist"], "references": []}