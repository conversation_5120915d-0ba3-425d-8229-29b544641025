services:
  background-removal:
    build:
      context: .
      dockerfile: cpu.Dockerfile
    ports:
      - "127.0.0.1:${PORT:-8000}:8000"
    env_file:
      - .env
    environment:
      - USE_GPU=0
      - PAYLOAD_URL=${PAYLOAD_URL}
      - REGISTRATION_KEY=${REGISTRATION_KEY}
      - TUNNEL_DOMAIN=${TUNNEL_DOMAIN}
    restart: unless-stopped
    networks:
      - tunnel_network

  cloudflared:
    image: cloudflare/cloudflared:latest
    command: tunnel --no-autoupdate run
    environment:
      - TUNNEL_TOKEN=${CLOUDFLARE_TUNNEL_TOKEN}
    depends_on:
      - background-removal
    restart: unless-stopped
    networks:
      - tunnel_network

networks:
  tunnel_network:
    driver: bridge 