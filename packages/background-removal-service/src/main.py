@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        if not background_removal:
            raise HTTPException(status_code=503, detail="Service not initialized")
            
        # Get system metrics from registration service
        metrics = await registration.get_system_metrics() if registration else None
        
        # Include current timestamp in ISO format
        current_time = datetime.utcnow().isoformat()
        
        return {
            "status": "healthy",
            "lastCheck": current_time,
            "latency": 0,  # This will be measured by the client
            "capabilities": {
                "gpu": app.state.has_gpu,
                "cpu": True,
                "memory": metrics["memory_total"] if metrics else None,
                "model": "u2net"
            },
            "load": {
                "cpu": metrics["cpu_percent"] if metrics else None,
                "memory": metrics["memory_percent"] if metrics else None,
                "gpu": metrics["gpu_load"] if metrics else None
            }
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unhealthy") 