import logging
import <PERSON><PERSON>til
from typing import List, NamedTuple

logger = logging.getLogger(__name__)

class GPUInfo(NamedTuple):
    """Information about an available GPU"""
    name: str
    memory_total: int
    memory_used: int
    load: float

def get_available_gpus() -> List[GPUInfo]:
    """Get information about available GPUs"""
    try:
        gpus = GPUtil.getGPUs()
        return [
            GPUInfo(
                name=gpu.name,
                memory_total=gpu.memoryTotal,
                memory_used=gpu.memoryUsed,
                load=gpu.load
            )
            for gpu in gpus
        ]
    except Exception as e:
        logger.error(f"Error detecting GPUs: {e}")
        return []

def should_use_gpu(use_gpu_env: bool) -> bool:
    """Determine if GPU should be used based on environment and availability"""
    if not use_gpu_env:
        logger.info("GPU usage disabled by environment variable")
        return False
        
    gpus = get_available_gpus()
    if not gpus:
        logger.warning("No GPUs detected on system")
        return False
        
    logger.info(f"Found {len(gpus)} GPU(s):")
    for gpu in gpus:
        logger.info(f"  - {gpu.name} ({gpu.memory_total}MB total memory)")
        
    return True 