from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, HTTPException, UploadFile, File
from fastapi.responses import Response
from fastapi.middleware.cors import CORSMiddleware
import logging
import asyncio
import uvicorn
from typing import Optional
from datetime import datetime

from .config import load_config, configure_warnings
from .gpu import should_use_gpu
from .remover import BackgroundRemovalService
from .registration import PayloadRegistration

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configure warnings
configure_warnings()

# Global state
background_removal: Optional[BackgroundRemovalService] = None
registration: Optional[PayloadRegistration] = None
status_update_task: Optional[asyncio.Task] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Startup and shutdown events for the FastAPI app"""
    logger.info("=== STARTUP ===")
    try:
        # Load configuration
        config = load_config()
        
        # Initialize GPU detection
        app.state.has_gpu = should_use_gpu(config["use_gpu"])
        
        # Initialize background removal service
        global background_removal
        background_removal = BackgroundRemovalService()
        await background_removal.initialize(device="cuda" if app.state.has_gpu else "cpu")
        
        # Initialize registration
        global registration
        registration = PayloadRegistration(
            app=app,
            payload_url=config["payload_url"],
            registration_key=config["registration_key"],
            tunnel_domain=config["tunnel_domain"]
        )
        
        # Initialize registration state
        app.state.is_registered = False
        app.state.endpoint_id = None
        app.state.ready = False  # Service is not ready until registration completes
        
        # Attempt registration
        if await registration.register():
            # Start status updates
            global status_update_task
            status_update_task = asyncio.create_task(registration.start_status_updates())
            app.state.ready = True  # Mark service as ready after successful registration
            logger.info("Service is now ready to accept requests")
        else:
            logger.warning("Failed to register with Payload CMS, continuing without registration")
            # Still mark as ready even if registration fails
            app.state.ready = True
            logger.info("Service is ready (without registration)")
            
        logger.info("Initialization complete")
        yield
        
    except Exception as e:
        logger.error(f"Error during startup: {e}")
        raise
    finally:
        logger.info("=== SHUTDOWN ===")
        if status_update_task:
            status_update_task.cancel()
            try:
                await status_update_task
            except asyncio.CancelledError:
                pass

# Create the FastAPI app
app = FastAPI(lifespan=lifespan)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
)

@app.post("/remove-background")
async def remove_background(image: UploadFile = File(...)):
    """Remove background from uploaded image"""
    if not background_removal:
        raise HTTPException(status_code=503, detail="Background removal service not initialized")
        
    if not app.state.ready:
        raise HTTPException(status_code=503, detail="Service is still initializing")
        
    try:
        # Read image data
        image_data = await image.read()
        
        # Process image
        result_data = background_removal.process_image(image_data)
        
        return Response(
            content=result_data,
            media_type="image/png",
            headers={
                "Content-Disposition": f"attachment; filename={image.filename.rsplit('.', 1)[0]}_no_bg.png"
            }
        )
        
    except Exception as e:
        logger.error(f"Error removing background: {e}")
        raise HTTPException(status_code=500, detail="Failed to process image")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        if not background_removal:
            raise HTTPException(status_code=503, detail="Service not initialized")
            
        # Get system metrics from registration service
        metrics = await registration.get_system_metrics() if registration else {
            "cpu_percent": 0,
            "memory_total": 0,
            "memory_percent": 0,
            "gpu_load": 0
        }
        
        # Include current timestamp in ISO format
        current_time = datetime.utcnow().isoformat()
        
        # Calculate time since startup
        startup_time = getattr(app.state, "startup_time", None)
        uptime_seconds = None
        if startup_time:
            uptime_seconds = (datetime.utcnow() - startup_time).total_seconds()
        
        # Format response exactly according to HealthCheckResponse type
        # IMPORTANT: Always set status to "healthy" if the service is ready
        # This is crucial for the Next.js client to recognize the endpoint as healthy
        response = {
            "status": "healthy",  # Always set to "healthy" if we can respond
            "ready": True,  # Explicitly indicate service is ready
            "lastCheck": current_time,
            "latency": 0,  # This will be measured by the client
            "uptime": uptime_seconds,
            "capabilities": {
                "gpu": app.state.has_gpu,
                "cpu": True,
                "memory": metrics["memory_total"],
                "model": "u2net"
            },
            "load": {
                "cpu": metrics["cpu_percent"],
                "memory": metrics["memory_percent"],
                "gpu": metrics["gpu_load"]
            }
        }
        
        return response
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail=f"Service unhealthy: {str(e)}")

async def start_server(host: str = "0.0.0.0", port: int = 8000, log_level: str = "info"):
    """Start the uvicorn server with the specified configuration"""
    # Record startup time
    app.state.startup_time = datetime.utcnow()
    
    config = uvicorn.Config(
        app=app,
        host=host,
        port=port,
        log_level=log_level,
        lifespan="on"
    )
    server = uvicorn.Server(config)
    await server.serve()

def run_server(host: str = "0.0.0.0", port: int = 8000, log_level: str = "info"):
    """Run the server (blocking)"""
    asyncio.run(start_server(host=host, port=port, log_level=log_level))

if __name__ == "__main__":
    # Load configuration for server settings
    config = load_config()
    run_server(port=config["port"]) 