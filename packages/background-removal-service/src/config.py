import os
from typing import TypedDict

class ServiceConfig(TypedDict):
    port: int
    use_gpu: bool
    registration_key: str
    payload_url: str
    tunnel_domain: str

def load_config() -> ServiceConfig:
    """Load and validate service configuration from environment variables"""
    config = {
        "port": int(os.getenv("PORT", "8000")),
        "use_gpu": os.getenv("USE_GPU", "0") == "1",
        "registration_key": os.getenv("REGISTRATION_KEY", ""),
        "payload_url": os.getenv("PAYLOAD_URL", ""),
        "tunnel_domain": os.getenv("TUNNEL_DOMAIN", ""),
    }
    
    # Validate required fields
    missing = [k for k, v in config.items() if not v and k not in ["port", "use_gpu"]]
    if missing:
        raise ValueError(f"Missing required environment variables: {', '.join(missing)}")
        
    return config

# Configure warning suppression
def configure_warnings():
    """Configure global warning suppression"""
    import warnings
    
    # Set environment variables
    os.environ['NO_ALBUMENTATIONS_UPDATE'] = '1'
    os.environ['PYTHONWARNINGS'] = 'ignore::FutureWarning'
    
    # Filter specific warnings
    warnings.filterwarnings('ignore', category=UserWarning, module='torch.functional')
    warnings.filterwarnings('ignore', category=FutureWarning, module='timm.models.layers') 