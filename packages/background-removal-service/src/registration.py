import os
import logging
import httpx
import psutil
from typing import Optional, Dict, Any
from .gpu import should_use_gpu, get_available_gpus
from datetime import datetime
from fastapi import FastAPI

logger = logging.getLogger(__name__)

class PayloadRegistration:
    def __init__(self, app: FastAPI, payload_url: str, registration_key: str, tunnel_domain: str):
        self.app = app
        self.payload_url = payload_url
        self.registration_key = registration_key
        self.tunnel_domain = tunnel_domain
        self.service_url = f"https://{tunnel_domain}"
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"api-tokens API-Key {registration_key}"
        }
        
    def _get_endpoint_data(self) -> Dict[str, Any]:
        """Get endpoint registration data"""
        return {
            "url": self.service_url,
            "priority": 100 if self.app.state.has_gpu else 50,
            "capabilities": {
                "gpu": self.app.state.has_gpu,
                "cpu": True,
                "memory": psutil.virtual_memory().total // (1024 * 1024),
                "model": "u2net"
            },
            "isLocal": False,
            "presharedKey": self.registration_key
        }

    def _get_gpu_metrics(self) -> Optional[Dict[str, float]]:
        """Get GPU metrics safely"""
        try:
            if not self.app.state.has_gpu:
                return None
            
            gpus = get_available_gpus()
            if not gpus:
                return None
                
            # Use the first GPU's metrics
            gpu = gpus[0]
            return {
                "memory_total": gpu.memory_total,
                "memory_used": gpu.memory_used,
                "load": gpu.load
            }
        except Exception as e:
            logger.warning(f"Failed to get GPU metrics: {e}")
            return None
        
    async def get_system_metrics(self) -> Dict[str, Any]:
        """Get system metrics for health check"""
        try:
            # Get CPU metrics
            cpu_percent = psutil.cpu_percent(interval=0.1)
            
            # Get memory metrics
            memory = psutil.virtual_memory()
            memory_total = memory.total // (1024 * 1024)  # Convert to MB
            memory_percent = memory.percent
            
            # Get GPU metrics if available
            gpu_metrics = self._get_gpu_metrics()
            gpu_load = 0
            if gpu_metrics:
                gpu_load = gpu_metrics["load"] * 100  # Convert to percentage
            
            return {
                "cpu_percent": cpu_percent,
                "memory_total": memory_total,
                "memory_percent": memory_percent,
                "gpu_load": gpu_load
            }
        except Exception as e:
            logger.error(f"Error getting system metrics: {e}")
            # Return default values if there's an error
            return {
                "cpu_percent": 0,
                "memory_total": 0,
                "memory_percent": 0,
                "gpu_load": 0
            }
        
    async def register(self) -> bool:
        """Register this service with Payload CMS"""
        timeout = httpx.Timeout(60.0, connect=30.0)
        
        try:
            async with httpx.AsyncClient(timeout=timeout) as client:
                # First check if we already have an endpoint with this URL
                logger.info("Checking for existing endpoint registration...")
                try:
                    response = await client.get(
                        f"{self.payload_url}/api/background-removal-endpoints",
                        headers=self.headers,
                        params={"where[url][equals]": self.service_url}
                    )
                    response.raise_for_status()
                    
                    existing_endpoints = response.json().get("docs", [])
                    if existing_endpoints:
                        # Update existing endpoint
                        endpoint_id = existing_endpoints[0]["id"]
                        self.app.state.endpoint_id = endpoint_id
                        logger.info(f"Found existing endpoint (id: {endpoint_id}), updating...")
                        
                        response = await client.patch(
                            f"{self.payload_url}/api/background-removal-endpoints/{endpoint_id}",
                            headers=self.headers,
                            json={
                                "name": f"Endpoint {self.service_url.split('//')[1]}",
                                "url": self.service_url,
                                "healthCheck": f"{self.service_url}/health",
                                "priority": 100 if self.app.state.has_gpu else 50,
                                "capabilities": {
                                    "gpu": self.app.state.has_gpu,
                                    "cpu": True,
                                    "memory": psutil.virtual_memory().total // (1024 * 1024),
                                    "model": "u2net"
                                },
                                "isLocal": False,
                                "isEnabled": True,
                                "status": {
                                    "healthy": True,
                                    "lastCheck": datetime.utcnow().isoformat(),
                                    "latency": 0,
                                    "load": {
                                        "cpu": psutil.cpu_percent(),
                                        "memory": psutil.virtual_memory().percent,
                                        "gpu": 0
                                    }
                                }
                            }
                        )
                        response.raise_for_status()
                        self.app.state.is_registered = True
                        logger.info(f"Successfully updated endpoint (id: {endpoint_id})")
                        return True
                except Exception as e:
                    logger.warning(f"Error checking for existing endpoint: {e}")
                
                # Create new endpoint
                logger.info("Creating new endpoint registration...")
                endpoint_data = {
                    "name": f"Endpoint {self.service_url.split('//')[1]}",
                    "url": self.service_url,
                    "healthCheck": f"{self.service_url}/health",
                    "priority": 100 if self.app.state.has_gpu else 50,
                    "provider": "custom",
                    "capabilities": {
                        "gpu": self.app.state.has_gpu,
                        "cpu": True,
                        "memory": psutil.virtual_memory().total // (1024 * 1024),
                        "model": "u2net"
                    },
                    "isLocal": False,
                    "isEnabled": True,
                    "apiKey": self.registration_key,
                    "status": {
                        "healthy": True,
                        "lastCheck": datetime.utcnow().isoformat(),
                        "latency": 0,
                        "load": {
                            "cpu": psutil.cpu_percent(),
                            "memory": psutil.virtual_memory().percent,
                            "gpu": 0
                        }
                    }
                }
                
                # Log the registration attempt details (without sensitive info)
                logger.info(f"Registration URL: {self.payload_url}/api/background-removal-endpoints")
                logger.info(f"Service URL: {self.service_url}")
                logger.info(f"Has GPU: {self.app.state.has_gpu}")
                
                response = await client.post(
                    f"{self.payload_url}/api/background-removal-endpoints",
                    headers=self.headers,
                    json=endpoint_data
                )
                response.raise_for_status()
                
                registration_response = response.json()
                self.app.state.endpoint_id = registration_response.get('id')
                self.app.state.is_registered = True
                logger.info(f"Successfully registered with Payload (id: {self.app.state.endpoint_id})")
                return True
                
        except httpx.TimeoutException:
            logger.error("Timeout while connecting to Payload CMS")
            return False
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error {e.response.status_code} from Payload: {e.response.text}")
            return False
        except Exception as e:
            logger.error(f"Error registering with Payload: {str(e)}")
            return False
            
    async def update_status(self) -> None:
        """Update endpoint status in Payload CMS"""
        if not self.app.state.is_registered or not self.app.state.endpoint_id:
            return
            
        try:
            # Get current system metrics
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            gpu_metrics = self._get_gpu_metrics()
            
            status_data = {
                "status": {
                    "healthy": True,
                    "lastCheck": datetime.utcnow().isoformat(),
                    "latency": 0,
                    "load": {
                        "cpu": cpu_percent,
                        "memory": memory.percent,
                        "gpu": 0 if not gpu_metrics else gpu_metrics["load"] * 100
                    }
                }
            }
            
            timeout = httpx.Timeout(10.0, connect=5.0)
            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.patch(
                    f"{self.payload_url}/api/background-removal-endpoints/{self.app.state.endpoint_id}",
                    headers=self.headers,
                    json=status_data
                )
                response.raise_for_status()
                logger.debug("Successfully updated endpoint status")
                
        except Exception as e:
            logger.error(f"Failed to update endpoint status: {str(e)}")
            
    async def start_status_updates(self) -> None:
        """Start periodic status updates"""
        import asyncio
        while True:
            await self.update_status()
            await asyncio.sleep(30)

async def register_with_server() -> None:
    """
    Register this service with the Payload server
    """
    try:
        payload_url = os.getenv("PAYLOAD_URL")
        registration_key = os.getenv("REGISTRATION_KEY")
        tunnel_domain = os.getenv("TUNNEL_DOMAIN")
        
        logger.info("=== REGISTRATION ATTEMPT ===")
        logger.info(f"PAYLOAD_URL: {payload_url if payload_url else '[MISSING]'}")
        logger.info(f"REGISTRATION_KEY: {'[REDACTED]' if registration_key else '[MISSING]'}")
        logger.info(f"TUNNEL_DOMAIN: {tunnel_domain if tunnel_domain else '[MISSING]'}")
        
        if not all([payload_url, registration_key, tunnel_domain]):
            missing_vars = [var for var, value in {
                "PAYLOAD_URL": payload_url,
                "REGISTRATION_KEY": registration_key,
                "TUNNEL_DOMAIN": tunnel_domain
            }.items() if not value]
            logger.error(f"Cannot register service: missing required environment variables: {', '.join(missing_vars)}")
            return
        
        service_url = f"https://{tunnel_domain}"
        logger.info(f"Service URL: {service_url}")
        
        # Create a registration instance and register
        from fastapi import FastAPI
        app = FastAPI()
        app.state.has_gpu = should_use_gpu
        app.state.is_registered = False
        app.state.endpoint_id = None
        
        registration = PayloadRegistration(app, payload_url, registration_key, tunnel_domain)
        success = await registration.register()
        
        if not success:
            logger.warning("Failed to register with Payload CMS, continuing without registration")
        else:
            logger.info("Service is registered and ready")
            
    except Exception as e:
        logger.error(f"Unexpected error during registration: {str(e)}")
        logger.warning("Failed to register with Payload CMS, continuing without registration") 