import logging
import numpy as np
import torch
from transparent_background import Remover
from PIL import Image
import io
from typing import Optional

logger = logging.getLogger(__name__)

class BackgroundRemovalService:
    def __init__(self):
        self.remover: Optional[Remover] = None
        self._setup_torch_meshgrid()
        
    def _setup_torch_meshgrid(self):
        """Configure torch.meshgrid to handle indexing parameter"""
        original_meshgrid = torch.meshgrid
        
        def new_meshgrid(*tensors, indexing=None):
            if indexing is None:
                return original_meshgrid(*tensors)
            return original_meshgrid(*tensors, indexing=indexing)
            
        torch.meshgrid = new_meshgrid
        
    async def initialize(self, device: str = "cpu") -> None:
        """Initialize the background remover"""
        try:
            self.remover = Remover()
            logger.info("Testing background remover initialization...")
            
            # Test with a small blank image
            test_img = np.zeros((64, 64, 3), dtype=np.uint8)
            _ = self.remover.process(test_img)
            logger.info(f"Successfully initialized background remover on {device}")
            
        except Exception as e:
            logger.error(f"Failed to initialize background remover: {e}")
            raise RuntimeError(f"Background remover initialization failed: {e}")
            
    def process_image(self, image_data: bytes) -> bytes:
        """Process an image and remove its background"""
        if not self.remover:
            raise RuntimeError("Background remover not initialized")
            
        try:
            # Load image
            img = Image.open(io.BytesIO(image_data))
            
            # Convert RGBA to RGB if needed
            if img.mode == 'RGBA':
                img = img.convert('RGB')
                
            # Remove background
            result = self.remover.process(np.array(img))
            
            # Convert back to PNG
            output = io.BytesIO()
            Image.fromarray(result).save(output, format='PNG')
            output.seek(0)
            
            return output.getvalue()
            
        except Exception as e:
            logger.error(f"Error removing background: {e}")
            raise 