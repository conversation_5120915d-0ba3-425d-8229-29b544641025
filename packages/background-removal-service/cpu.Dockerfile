FROM python:3.10-slim AS builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    python3-dev \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Create and activate virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Upgrade pip and install build tools
RUN pip install --no-cache-dir -U pip setuptools wheel

# Install PyTorch CPU version first with increased timeout
RUN pip install --no-cache-dir --timeout 300 \
    torch==2.1.2+cpu \
    torchvision==0.16.2+cpu \
    --extra-index-url https://download.pytorch.org/whl/cpu

# Copy requirements and install in batches
COPY requirements.txt .

# Install typing-extensions first to ensure correct version
RUN pip install --no-cache-dir --timeout 300 typing-extensions==4.12.2

# Install FastAPI and core dependencies
RUN pip install --no-cache-dir --timeout 300 \
    fastapi==0.109.0 \
    uvicorn==0.27.0 \
    python-multipart==0.0.6 \
    httpx==0.26.0

# Install numerical and image processing dependencies
RUN pip install --no-cache-dir --timeout 300 \
    psutil==5.9.8 \
    gputil==1.4.0 \
    pillow==10.2.0 \
    numpy==1.26.4

# Install ML dependencies
RUN pip install --no-cache-dir --timeout 300 \
    python-json-logger==2.0.7 \
    albumentations==1.3.1 \
    timm==0.9.12

# Install transparent-background last
RUN pip install --no-cache-dir --timeout 300 \
    transparent-background==1.3.3

# Final stage
FROM python:3.10-slim

# Install runtime dependencies only
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    && rm -rf /var/lib/apt/lists/*

# Copy virtual environment from builder
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Set environment variables to suppress warnings
ENV NO_ALBUMENTATIONS_UPDATE=1
ENV PYTHONWARNINGS="ignore::FutureWarning"
ENV TORCH_WARN_ONCE=1

# Set working directory
WORKDIR /app

# Copy application code
COPY src ./src

# Run the application
CMD ["python", "-m", "src.app", "--host", "0.0.0.0", "--port", "8000"] 