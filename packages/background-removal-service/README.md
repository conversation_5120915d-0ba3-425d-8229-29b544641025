# Background Removal Service

A scalable background removal service that supports both CPU and GPU processing, using Cloudflare Tunnel for secure access.

## Features

- 🖼️ High-quality background removal using the `transparent-background` package
- 🚀 GPU acceleration support (when available)
- 🔒 Secure access via Cloudflare Tunnel
- 🔑 API key authentication
- 📊 Health monitoring and metrics

## Prerequisites

- Docker and Docker Compose
- Cloudflare account with Zero Trust access
- NVIDIA GPU (optional, for GPU acceleration)
- NVIDIA Container Toolkit (if using GPU)

## Quick Start

1. **Create API Key in Payload Admin**
   ```bash
   # Navigate to your Payload admin panel
   # Create a new API token for registration
   # This token will be used as REGISTRATION_KEY
   ```

2. **Set Up Cloudflare Tunnel**
   1. Go to [Cloudflare Zero Trust Dashboard](https://one.dash.cloudflare.com)
   2. Navigate to Access > Tunnels
   3. Click "Create a tunnel"
   4. Give your tunnel a name (e.g., "background-removal")
   5. Copy the provided tunnel token
   6. Under "Public Hostname", configure:
      - Subdomain: Choose a subdomain for your service
      - Domain: Select your domain
      - Service: `http://background-removal:8000`
   7. Note the full domain (e.g., `background-remove.yourdomain.com`)

3. **Configure Environment**
   ```bash
   # Copy example environment file
   cp .env.example .env

   # Edit .env and add:
   # - REGISTRATION_KEY (from Payload admin)
   # - CLOUDFLARE_TUNNEL_TOKEN (from Cloudflare Zero Trust dashboard)
   # - TUNNEL_DOMAIN (the full domain you configured for your tunnel)
   ```

4. **Start the Service**
   ```bash
   # CPU mode (default)
   docker compose up -d

   # GPU mode (requires NVIDIA Container Toolkit)
   USE_GPU=1 docker compose up -d
   ```

## GPU Support

To enable GPU support:

1. Install NVIDIA Container Toolkit:
   ```bash
   # Ubuntu/Debian
   curl -fsSL https://nvidia.github.io/libnvidia-container/gpgkey | sudo gpg --dearmor -o /usr/share/keyrings/nvidia-container-toolkit-keyring.gpg
   curl -s -L https://nvidia.github.io/libnvidia-container/stable/deb/nvidia-container-toolkit.list | \
     sed 's#deb https://#deb [signed-by=/usr/share/keyrings/nvidia-container-toolkit-keyring.gpg] https://#g' | \
     sudo tee /etc/apt/sources.list.d/nvidia-container-toolkit.list
   sudo apt update
   sudo apt install -y nvidia-container-toolkit
   sudo nvidia-ctk runtime configure --runtime=docker
   sudo systemctl restart docker
   ```

2. Start the service with GPU support:
   ```bash
   USE_GPU=1 docker compose up -d
   ```

## Dependencies

The service uses the following main Python packages:
- FastAPI (0.109.0)
- Uvicorn (0.27.0)
- transparent-background (1.2.11)
- GPUtil (1.4.0)
- psutil (5.9.8)
- httpx (0.26.0)

Dependencies are managed via `requirements.txt`. To update dependencies:
1. Update versions in `requirements.txt`
2. Rebuild the container: `docker compose build background-removal`

## API Usage

The service exposes two endpoints:

### Health Check
```bash
curl -X GET https://${TUNNEL_DOMAIN}/health \
  -H "X-API-Key: your-endpoint-api-key"
```

### Remove Background
```bash
curl -X POST https://${TUNNEL_DOMAIN}/remove-background \
  -H "X-API-Key: your-endpoint-api-key" \
  -F "image=@path/to/your/image.jpg"
```

## Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `REGISTRATION_KEY` | API key from Payload admin for endpoint registration | Yes |
| `PAYLOAD_URL` | URL of your Payload CMS | Yes |
| `CLOUDFLARE_TUNNEL_TOKEN` | Your Cloudflare Tunnel token | Yes |
| `TUNNEL_DOMAIN` | Full domain for your tunnel | Yes |
| `USE_GPU` | Enable GPU support (0/1) | No |
| `PORT` | Local port for service | No |

## Monitoring

The service reports the following metrics via the health endpoint:
- CPU usage
- Memory usage
- GPU usage (if available)
- System capabilities
- Service status

## Troubleshooting

1. **GPU Not Detected**
   - Check NVIDIA drivers are installed: `nvidia-smi`
   - Verify NVIDIA Container Toolkit: `docker run --rm --gpus all nvidia/cuda:12.4.1-base-ubuntu22.04 nvidia-smi`
   - Make sure you're using GPU mode: `USE_GPU=1 docker compose up -d`

2. **Tunnel Connection Issues**
   - Check tunnel status: `docker compose logs cloudflared`
   - Verify tunnel token is correct
   - Check tunnel status in Cloudflare Zero Trust dashboard
   - Verify TUNNEL_DOMAIN matches your Cloudflare configuration

3. **API Key Issues**
   - Confirm REGISTRATION_KEY matches Payload admin token
   - Check API key permissions in Payload
   - Verify endpoint registration in Payload admin
