{"name": "@stitchestimate/web", "version": "1.0.0", "description": "A website for Embroidery Stitch Estimates", "license": "UNLICENSED", "type": "module", "packageManager": "pnpm@10.8.1", "private": true, "scripts": {"predev": "docker compose up -d postgres", "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev --turbopack", "postdev": "docker compose stop postgres", "predev:test": "cross-env NODE_ENV=test bash -c \"docker compose up -d postgres-test && echo 'yes' | pnpm run payload migrate:fresh\"", "dev:test": "cross-env NODE_ENV=test NODE_OPTIONS=--no-deprecation next dev -p 4000", "postdev:test": "docker compose stop postgres-test", "dev:prod": "cross-env NODE_OPTIONS=--no-deprecation rm -rf .next && pnpm build && pnpm start", "prebuild": "if [ \"$CI\" != \"true\" ] && [ \"$VERCEL\" != \"1\" ]; then docker compose up -d postgres; fi", "build": "cross-env NODE_OPTIONS=--no-deprecation next build", "postbuild": "next-sitemap --config next-sitemap.config.cjs && if [ \"$CI\" != \"true\" ] && [ \"$VERCEL\" != \"1\" ]; then docker compose stop postgres; fi", "ci": "cross-env CI=true NODE_OPTIONS=--no-deprecation payload migrate || true && pnpm run upload:static || true && pnpm build", "db:start": "docker compose up -d postgres", "db:stop": "docker compose stop postgres", "db:restart": "pnpm db:stop && pnpm db:start", "test": "vitest run", "test:watch": "vitest watch", "test:coverage": "vitest run --coverage", "test:e2e": "cross-env NODE_ENV=test PAYLOAD_SECRET=test-secret NODE_OPTIONS=--no-deprecation playwright test", "test:e2e:ui": "cross-env NODE_ENV=test PAYLOAD_SECRET=test-secret NODE_OPTIONS=--no-deprecation playwright test --ui", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint", "lint:fix": "cross-env NODE_OPTIONS=--no-deprecation next lint --fix", "typecheck": "cross-env NODE_OPTIONS=--max-old-space-size=8192 tsc --noEmit", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "reset:admin": "cross-env NODE_OPTIONS=--no-deprecation payload run scripts/reset-admin.ts", "list:users": "cross-env NODE_OPTIONS=--no-deprecation payload run scripts/list-users.ts", "upload:static": "node scripts/upload-static-assets.js", "cloudflare": "cloudflared tunnel run", "kill": "pkill -f stitchestimate", "preinstall": "npx only-allow pnpm", "ii": "cross-env NODE_OPTIONS=--no-deprecation pnpm --ignore-workspace install", "reinstall": "cross-env NODE_OPTIONS=--no-deprecation rm -rf node_modules && rm pnpm-lock.yaml && pnpm --ignore-workspace install"}, "dependencies": {"@stitchestimate/embroidery-processor": "workspace:*", "@aws-sdk/client-s3": "^3.803.0", "@aws-sdk/s3-request-presigner": "^3.803.0", "@huggingface/transformers": "^3.5.1", "@imgly/background-removal": "1.5.5", "@lexical/headless": "^0.21.0", "@lexical/html": "^0.21.0", "@lexical/link": "^0.21.0", "@lexical/list": "^0.21.0", "@lexical/mark": "^0.21.0", "@lexical/react": "^0.21.0", "@lexical/rich-text": "^0.21.0", "@lexical/selection": "^0.21.0", "@lexical/table": "^0.21.0", "@lexical/utils": "^0.21.0", "@paddle/paddle-js": "^1.4.1", "@paddle/paddle-node-sdk": "^2.7.0", "@payloadcms/db-postgres": "^3.37.0", "@payloadcms/live-preview-react": "^3.37.0", "@payloadcms/next": "^3.37.0", "@payloadcms/payload-cloud": "^3.37.0", "@payloadcms/plugin-cloud-storage": "^3.37.0", "@payloadcms/plugin-form-builder": "^3.37.0", "@payloadcms/plugin-nested-docs": "^3.37.0", "@payloadcms/plugin-redirects": "^3.37.0", "@payloadcms/plugin-search": "^3.37.0", "@payloadcms/plugin-seo": "^3.37.0", "@payloadcms/richtext-lexical": "^3.37.0", "@payloadcms/storage-s3": "^3.37.0", "@payloadcms/ui": "^3.37.0", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-tooltip": "^1.2.6", "@tanstack/react-table": "^8.21.3", "@types/lodash": "^4.17.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cross-env": "^7.0.3", "csv-writer": "^1.6.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "fabric": "^5.5.2", "geist": "^1.4.2", "graphql": "^16.11.0", "jbinary": "^2.1.5", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "lexical": "^0.21.0", "lodash": "^4.17.21", "lucide-react": "^0.378.0", "mime-types": "^3.0.1", "next": "^15.3.1", "next-sitemap": "^4.2.3", "onnxruntime-web": "^1.21.1", "payload": "^3.37.0", "payload-admin-bar": "^1.0.7", "pdf-lib": "^1.17.1", "prism-react-renderer": "^2.4.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.2", "sharp": "^0.32.6", "sonner": "^1.7.4", "svgson": "^5.3.1", "swr": "^2.3.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.4"}, "devDependencies": {"@csstools/css-calc": "^2.1.3", "@eslint/eslintrc": "^3.3.1", "@next/env": "^15.3.1", "@playwright/test": "^1.52.0", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/escape-html": "^1.0.4", "@types/fabric": "^5.3.10", "@types/jbinary": "^2.1.4", "@types/jsonwebtoken": "^9.0.9", "@types/mime-types": "^2.1.4", "@types/node": "^22.15.13", "@types/react": "^19.1.3", "@types/react-dom": "^19.1.3", "@vitejs/plugin-react": "^4.4.1", "@vitest/coverage-v8": "^1.6.1", "autoprefixer": "^10.4.21", "canvas": "^3.1.0", "copyfiles": "^2.4.1", "dotenv": "^16.5.0", "dotenv-cli": "^8.0.0", "eslint": "^9.26.0", "eslint-config-next": "15.1.0", "jsdom": "^24.1.3", "madge": "^8.0.0", "next-themes": "^0.4.6", "postcss": "^8.5.3", "prettier": "^3.5.3", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "tsx": "^4.19.4", "typescript": "5.7.2", "vite-tsconfig-paths": "^5.1.4", "vitest": "^1.6.1"}, "engines": {"node": "20.x"}}