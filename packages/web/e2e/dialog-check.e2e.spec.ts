import { test, expect } from '@playwright/test'
import path from 'path'
import fs from 'fs'
import { fileURLToPath } from 'url'

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const rootDir = path.resolve(__dirname, '..')

// Ensure test-results directory exists
const screenshotDir = path.join(rootDir, 'test-results')
if (!fs.existsSync(screenshotDir)) {
  fs.mkdirSync(screenshotDir, { recursive: true })
}

test('check dialog structure', async ({ page }) => {
  await page.goto('/stitch-file-preview')
  await page.waitForLoadState('domcontentloaded')

  // Upload a sample file
  const fileInput = page.getByTestId('file-input')
  await fileInput.setInputFiles(path.join(rootDir, 'public/test-assets/sample.dst'))

  // Wait for file to be processed
  await page.waitForTimeout(2000)

  // Open global settings
  await page.getByTestId('global-settings-button').click()

  // Take a screenshot
  await page.screenshot({ path: path.join(screenshotDir, 'dialog-structure.png') })

  // Print dialog HTML
  const dialogHTML = await page.evaluate(() => {
    const dialogs = document.querySelectorAll('[data-testid="dialog"]')
    return Array.from(dialogs)
      .map((dialog) => dialog.outerHTML)
      .join('\n\n')
  })

  console.log('Dialog HTML:')
  console.log(dialogHTML)

  // List all test IDs
  const testIds = await page.evaluate(() => {
    const elements = document.querySelectorAll('[data-testid]')
    return Array.from(elements).map((el) => el.getAttribute('data-testid'))
  })

  console.log('All test IDs:')
  console.log(testIds)
})
