import { test, expect, Page } from '@playwright/test'
import { createTestUser } from './utils/auth'

// Selectors used across tests
const SELECTORS = {
  HEADINGS: {
    WELCOME: 'Welcome back',
    SIGN_IN: 'Sign in to your account',
  },
  INPUTS: {
    EMAIL: 'Email',
    PASSWORD: 'Password',
  },
  BUTTONS: {
    SIGN_IN: 'Sign in',
    CREATE_ACCOUNT: 'Create account',
  },
  ERROR_MESSAGE: 'The email or password provided is incorrect.',
} as const

/**
 * Helper function to fill out the login form
 */
async function fillLoginForm(page: Page, { email, password }: { email: string; password: string }) {
  await page.getByLabel(SELECTORS.INPUTS.EMAIL).fill(email)
  await page.getByLabel(SELECTORS.INPUTS.PASSWORD).fill(password)
}

/**
 * Helper function to create a test user with unique email
 */
async function createUniqueTestUser() {
  const uniqueId = Date.now()
  const testUser = {
    name: 'Test User',
    email: `test.user.${uniqueId}@example.com`,
    password: 'TestPassword123!',
  }

  await createTestUser(testUser)
  return testUser
}

test.describe('Login Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Start from login page and enable console logging
    await page.goto('/login')
    page.on('console', (msg) => console.log(`Browser console: ${msg.text()}`))
  })

  test('should display login form correctly', async ({ page }) => {
    // Verify page title and description
    await expect(page.getByRole('heading', { name: SELECTORS.HEADINGS.WELCOME })).toBeVisible()
    await expect(page.getByText(SELECTORS.HEADINGS.SIGN_IN)).toBeVisible()

    // Verify form fields
    await expect(page.getByLabel(SELECTORS.INPUTS.EMAIL)).toBeVisible()
    await expect(page.getByLabel(SELECTORS.INPUTS.PASSWORD)).toBeVisible()
    await expect(page.getByRole('button', { name: SELECTORS.BUTTONS.SIGN_IN })).toBeVisible()

    // Verify signup link
    await expect(page.getByRole('link', { name: SELECTORS.BUTTONS.CREATE_ACCOUNT })).toBeVisible()
  })

  test('should handle successful login', async ({ page }) => {
    // Create a unique test user
    const testUser = await createUniqueTestUser()

    // Fill out and submit the login form
    await fillLoginForm(page, testUser)

    // Set up a navigation listener before clicking the login button
    const navigationPromise = page.waitForURL('**/account')

    // Click the login button
    await page.getByRole('button', { name: SELECTORS.BUTTONS.SIGN_IN }).click()

    // Wait for the navigation to complete
    await navigationPromise

    // Verify that we're on the account page
    await expect(page).toHaveURL(/.*\/account/)
  })

  test('should handle invalid credentials', async ({ page }) => {
    // Test with invalid credentials
    await fillLoginForm(page, {
      email: '<EMAIL>',
      password: 'wrongpassword',
    })

    // Click the login button and wait for the API response
    await page.getByRole('button', { name: SELECTORS.BUTTONS.SIGN_IN }).click()

    // Wait for the error message to appear with a longer timeout
    await expect(page.getByText(SELECTORS.ERROR_MESSAGE)).toBeVisible({ timeout: 10000 })
  })

  test('should handle empty form submission', async ({ page }) => {
    // Submit empty form
    await page.getByRole('button', { name: SELECTORS.BUTTONS.SIGN_IN }).click()

    // Verify browser validation messages
    const emailInput = page.getByLabel(SELECTORS.INPUTS.EMAIL)
    const isEmailRequired = await emailInput.evaluate((el) => el.hasAttribute('required'))
    expect(isEmailRequired).toBeTruthy()

    const passwordInput = page.getByLabel(SELECTORS.INPUTS.PASSWORD)
    const isPasswordRequired = await passwordInput.evaluate((el) => el.hasAttribute('required'))
    expect(isPasswordRequired).toBeTruthy()
  })

  test('should handle redirect parameter', async ({ page }) => {
    // Navigate to login with redirect parameter
    const redirectPath = '/checkout/price_123'
    await page.goto(`/login?redirect=${redirectPath}`)

    // Create and login with test user
    const testUser = await createUniqueTestUser()
    await fillLoginForm(page, testUser)

    // Submit form and verify redirect
    await Promise.all([
      page.waitForURL(`**${redirectPath}`),
      page.getByRole('button', { name: SELECTORS.BUTTONS.SIGN_IN }).click(),
    ])
  })

  test('should navigate to signup page with preserved redirect', async ({ page }) => {
    // Test basic navigation
    await Promise.all([
      page.waitForURL('**/signup?redirect=/account'),
      page.getByRole('link', { name: SELECTORS.BUTTONS.CREATE_ACCOUNT }).click(),
    ])

    // Test with custom redirect parameter
    const redirectPath = '/checkout/price_123'
    await page.goto(`/login?redirect=${redirectPath}`)
    await Promise.all([
      page.waitForURL(`**/signup?redirect=${redirectPath}`),
      page.getByRole('link', { name: SELECTORS.BUTTONS.CREATE_ACCOUNT }).click(),
    ])
  })
})
