import { TEST_GARMENT_TYPES } from '../fixtures/garment-types'
import { getAuthToken } from './auth'
import { getPayload } from 'payload'
import config from '@payload-config'
import { TEST_DEMO_IMAGES } from '../fixtures/demo-images'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const rootDir = path.resolve(__dirname, '../..')

// Default to localhost:4000 if no server URL is provided
const SERVER_URL = process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:4000'

/**
 * Seeds the database with test data
 */
export async function seedTestData() {
  const token = await getAuthToken()
  if (!token) {
    console.error('Failed to authenticate, cannot seed data')
    return
  }

  // Clean up existing data first
  await cleanupTestData()

  // Seed garment types
  for (const garmentType of TEST_GARMENT_TYPES) {
    try {
      const response = await fetch(`${SERVER_URL}/api/garment-types`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `JWT ${token}`,
        },
        body: JSON.stringify(garmentType),
      })

      if (!response.ok) {
        console.error(`Failed to seed garment type ${garmentType.name}:`, await response.text())
      }
    } catch (error) {
      console.error(`Error seeding garment type ${garmentType.name}:`, error)
    }
  }
}

/**
 * Cleans up test data from the database
 */
export async function cleanupTestData() {
  const token = await getAuthToken()
  if (!token) {
    console.error('Failed to authenticate, cannot cleanup data')
    return
  }

  // Clean up garment types
  for (const garmentType of TEST_GARMENT_TYPES) {
    try {
      // First try to find the garment type by slug
      const response = await fetch(
        `${SERVER_URL}/api/garment-types?where[slug][equals]=${garmentType.slug}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `JWT ${token}`,
          },
        },
      )

      if (response.ok) {
        const data = await response.json()
        if (data.docs.length > 0) {
          // Delete the existing garment type
          const deleteResponse = await fetch(`${SERVER_URL}/api/garment-types/${data.docs[0].id}`, {
            method: 'DELETE',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `JWT ${token}`,
            },
          })

          if (!deleteResponse.ok) {
            console.error(
              `Failed to delete garment type ${garmentType.name}:`,
              await deleteResponse.text(),
            )
          }
        }
      }
    } catch (error) {
      console.error(`Error cleaning up garment type ${garmentType.name}:`, error)
    }
  }
}

export async function seedDemoImages() {
  try {
    const payload = await getPayload({ config })

    // Clear existing demo images
    const existingImages = await payload.find({
      collection: 'demo-images',
      limit: 100,
    })

    for (const image of existingImages.docs) {
      await payload.delete({
        collection: 'demo-images',
        id: image.id,
      })
    }

    // Create new demo images
    for (const demoImage of TEST_DEMO_IMAGES) {
      // Read the test image file from the correct directory
      const filePath = path.join(rootDir, 'public/test-assets', demoImage.filename)
      const fileBuffer = fs.readFileSync(filePath)
      const fileStats = fs.statSync(filePath)

      // Determine mimetype based on file extension
      const ext = path.extname(demoImage.filename).toLowerCase()
      const mimetype = ext === '.png' ? 'image/png' : 'image/jpeg'

      await payload.create({
        collection: 'demo-images',
        data: {
          title: demoImage.title,
          description: demoImage.description,
        },
        file: {
          data: fileBuffer,
          mimetype,
          name: demoImage.filename,
          size: fileStats.size,
        },
      })
    }

    console.log('Demo images seeded successfully')
  } catch (error) {
    console.error('Failed to seed demo images:', error)
    throw error
  }
}
