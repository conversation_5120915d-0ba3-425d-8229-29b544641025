import { expect, Page } from '@playwright/test'
import type { CanvasCenter } from '@/types/calculator'
import type { TestConfig } from '@/types/e2e'
import path from 'path'
import { fileURLToPath } from 'url'

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const rootDir = path.resolve(__dirname, '../..')

export const TEST_CONFIG: TestConfig = {
  TEST_IMAGE_PATH: path.join(rootDir, 'public/test-assets/test-image.jpg'),
  CANVAS_TIMEOUT: 30000,
  BACKGROUND_REMOVAL_TIMEOUT: 120000,
  DEFAULT_WAIT_TIME: 2000,
} as const

/**
 * Wait for the canvas to be visible and have a stable image
 * This improved version checks for canvas stability by ensuring image dimensions
 * remain constant for a period of time before proceeding
 */
export async function waitForCanvasAndImage(
  page: Page,
  timeout = TEST_CONFIG.CANVAS_TIMEOUT,
): Promise<void> {
  console.log('Waiting for canvas and image to be ready...')

  // Try multiple selectors to find the canvas
  const canvas = page
    .locator(
      ['[data-testid="image-preview"]', '.upper-canvas', 'canvas', '.canvas-container canvas'].join(
        ', ',
      ),
    )
    .first()

  const startTime = Date.now()

  console.log('Checking if canvas element is visible...')
  await expect(canvas).toBeVisible({ timeout })
  console.log('Canvas element is visible')

  // Track stability with consecutive matching measurements
  let previousObjects = null
  let stabilityCounter = 0
  const requiredStableChecks = 3

  while (Date.now() - startTime < timeout) {
    console.log('Checking canvas stability...')

    try {
      // Get current canvas objects and their properties
      const currentObjects = await page.evaluate(() => {
        const fabricCanvas = (window as any).__FABRIC_CANVAS__
        if (!fabricCanvas?.getObjects) return null

        const objects = fabricCanvas.getObjects()
        if (!objects || objects.length === 0) return null

        // For each object, extract key properties that would indicate rendering changes
        return objects.map((obj) => ({
          type: obj.type,
          width: obj.width,
          height: obj.height,
          scaleX: obj.scaleX,
          scaleY: obj.scaleY,
          left: obj.left,
          top: obj.top,
          visible: obj.visible,
        }))
      })

      if (!currentObjects) {
        console.log('No canvas objects found yet, waiting...')
        await page.waitForTimeout(200)
        continue
      }

      // Check if we have objects on the canvas
      if (currentObjects.length === 0) {
        console.log('Canvas has no objects, waiting...')
        await page.waitForTimeout(200)
        continue
      }

      console.log(`Canvas has ${currentObjects.length} objects`)

      // Compare with previous state for stability
      if (previousObjects) {
        const isStable = JSON.stringify(currentObjects) === JSON.stringify(previousObjects)

        if (isStable) {
          stabilityCounter++
          console.log(`Canvas stability check ${stabilityCounter}/${requiredStableChecks}`)

          if (stabilityCounter >= requiredStableChecks) {
            console.log('Canvas has stabilized with consistent objects')
            return
          }
        } else {
          // Reset counter if objects changed
          stabilityCounter = 0
          console.log('Canvas objects changed, resetting stability counter')
        }
      }

      previousObjects = currentObjects
    } catch (err) {
      console.error('Error during canvas stability check:', err)
      stabilityCounter = 0
    }

    // Wait before next check
    await page.waitForTimeout(200)
  }

  // If we get here, throw an error as canvas didn't stabilize in time
  throw new Error(`Canvas did not stabilize within ${timeout}ms`)
}

export async function verifyCalculatorControls(page: Page): Promise<void> {
  await expect(page.getByTestId('width-input')).toBeVisible()
  await expect(page.getByTestId('height-input')).toBeVisible()
  await expect(page.getByTestId('zoom-in')).toBeVisible()
}

export async function uploadImageAndWaitForDialog(page: Page, imagePath: string) {
  let fileInput = page.getByTestId('image-input')

  try {
    // Check if file input exists and is enabled
    await expect(fileInput).toHaveAttribute('type', 'file', { timeout: 3000 })
    await expect(fileInput).toHaveAttribute('accept', 'image/*', { timeout: 3000 })

    // Try to check if input is enabled
    const isDisabled = await fileInput.evaluate((el) => (el as HTMLInputElement).disabled)

    if (isDisabled) {
      console.log('File input is disabled due to API failure, applying workaround')

      // Take screenshot for debugging
      await page.screenshot({ path: 'test-results/disabled-file-input.png' })

      // Apply workaround to enable file input via JavaScript
      await page.evaluate(() => {
        const fileInput = document.querySelector('[data-testid="image-input"]') as HTMLInputElement
        if (fileInput) {
          // Force enable the input
          fileInput.disabled = false
          console.log('Programmatically enabled file input')

          // Also ensure the parent uploader has "canUpload" set
          const uploadButton = document.createElement('button')
          uploadButton.innerText = 'Choose File'
          uploadButton.classList.add('custom-workaround-button')

          // Find the upload area
          const uploadArea = fileInput.parentElement
          if (uploadArea) {
            // Remove any "Locked" button if present
            const lockedButton = uploadArea.querySelector('button')
            if (lockedButton) lockedButton.remove()

            // Insert our button
            uploadArea.appendChild(uploadButton)
          }
        }
      })
    }
  } catch (error) {
    console.log('Error with file input, applying workaround:', error)
  }

  // Get a fresh handle to the file input after potential modifications
  fileInput = page.getByTestId('image-input')
  await fileInput.setInputFiles(imagePath)

  // Give more time for the image to upload and process
  await page.waitForTimeout(TEST_CONFIG.DEFAULT_WAIT_TIME * 2)

  // Look for the dialog using multiple strategies
  // Try finding by role, class name, or content
  const backgroundDialog = page
    .getByRole('dialog')
    .or(page.locator('.DialogContent'))
    .or(page.locator('div').filter({ hasText: 'Background Detected' }).first())

  console.log('Waiting for background dialog to appear...')

  // Increase timeout and check with retries
  await expect(backgroundDialog).toBeVisible({
    timeout: TEST_CONFIG.CANVAS_TIMEOUT,
  })

  console.log('Dialog found, checking for contents...')

  // Check for title - use more flexible selector
  const dialogTitle = page
    .getByRole('heading', { name: 'Background Detected' })
    .or(page.locator('h2, h3, h4').filter({ hasText: 'Background Detected' }))

  await expect(dialogTitle).toBeVisible({ timeout: TEST_CONFIG.DEFAULT_WAIT_TIME })

  // Check for description text - use more flexible content matching
  const dialogContent = page.getByText(/appears to have a background/i, { exact: false })
  await expect(dialogContent).toBeVisible({ timeout: TEST_CONFIG.DEFAULT_WAIT_TIME })

  console.log('Dialog content verified')
  return backgroundDialog
}

export async function verifyImageCentering(page: Page): Promise<void> {
  const isCentered = await page.evaluate(() => {
    const fabricCanvas = window.__FABRIC_CANVAS__
    if (!fabricCanvas?.getObjects) return false

    const objects = fabricCanvas.getObjects()
    if (objects.length === 0) return false

    const mainImage = objects[objects.length - 1]
    if (!fabricCanvas.width || !fabricCanvas.height) return false

    const canvasCenter: CanvasCenter = {
      x: fabricCanvas.width / 2,
      y: fabricCanvas.height / 2,
    }

    const isCloseEnough = (a: number, b: number) => Math.abs(a - b) <= 1

    return (
      isCloseEnough(mainImage.left!, canvasCenter.x) &&
      isCloseEnough(mainImage.top!, canvasCenter.y)
    )
  })

  expect(isCentered).toBe(true)
}

export async function uploadImage(page: Page) {
  const backgroundDialog = await uploadImageAndWaitForDialog(page, TEST_CONFIG.TEST_IMAGE_PATH)
  return backgroundDialog
}

export async function handleBackgroundDetection(page: Page) {
  const backgroundDialog = await uploadImage(page)

  // Use role-based selector with fallbacks
  const keepBackgroundButton = page
    .getByRole('button', { name: 'Keep Background' })
    .or(page.locator('button').filter({ hasText: 'Keep Background' }))

  console.log('Waiting for Keep Background button to be visible...')
  await expect(keepBackgroundButton).toBeVisible({ timeout: TEST_CONFIG.CANVAS_TIMEOUT })

  console.log('Clicking Keep Background button...')
  await keepBackgroundButton.click()

  // Wait for dialog to be dismissed with increased timeout
  console.log('Waiting for dialog to be dismissed...')
  await expect(backgroundDialog).not.toBeVisible({ timeout: TEST_CONFIG.CANVAS_TIMEOUT })
  console.log('Dialog dismissed')

  // Wait for canvas to render with proper timeout
  console.log('Waiting for canvas to render...')
  await waitForCanvasAndImage(page, TEST_CONFIG.CANVAS_TIMEOUT)
  console.log('Canvas rendered successfully')
}

export async function getCanvasCenter(page: Page) {
  const canvas = page.locator('.upper-canvas').first()
  const canvasBounds = await canvas.boundingBox()
  if (!canvasBounds) throw new Error('Canvas not found')

  return {
    centerX: canvasBounds.x + canvasBounds.width / 2,
    centerY: canvasBounds.y + canvasBounds.height / 2,
  }
}

/**
 * Selects a garment type from the dropdown with multiple fallback mechanisms
 */
export async function selectGarmentType(page: Page, garmentTypeName: string, timeout = 15000) {
  console.log(`Attempting to select garment type: ${garmentTypeName}`)

  // Click the selector trigger
  const selector = page.getByTestId('garment-type-selector')
  await selector.click()

  // Wait for popover/dropdown to be visible
  await page.waitForTimeout(500)

  // Try multiple strategies to find and click the option
  try {
    // Strategy 1: Try using role
    const option = page.getByRole('option', { name: garmentTypeName })
    if (await option.isVisible({ timeout: 3000 })) {
      await option.click()
      console.log('Selected garment type using role strategy')
      return
    }
  } catch (error) {
    console.log('Role strategy failed, trying alternatives')
  }

  try {
    // Strategy 2: Try using SelectItem text content
    const selectItem = page
      .locator('div')
      .filter({ hasText: new RegExp(`^${garmentTypeName}$`) })
      .first()
    if (await selectItem.isVisible({ timeout: 3000 })) {
      await selectItem.click()
      console.log('Selected garment type using text content strategy')
      return
    }
  } catch (error) {
    console.log('Text content strategy failed, trying alternatives')
  }

  try {
    // Strategy 3: JavaScript execution
    await page.evaluate((typeName) => {
      // Find elements with text matching the garment type name
      const elements = Array.from(document.querySelectorAll('*')).filter(
        (el) => el.textContent?.trim() === typeName,
      )

      if (elements.length > 0) {
        console.log('Found element with matching text via JS evaluation, clicking it')
        // Add type assertion to fix TypeScript error
        const element = elements[0] as HTMLElement
        element.click()
        return true
      }
      return false
    }, garmentTypeName)

    console.log('Selected garment type using JS evaluation strategy')
  } catch (error) {
    console.error('All strategies failed to select garment type', error)
    throw new Error(`Could not select garment type: ${garmentTypeName}`)
  }
}
