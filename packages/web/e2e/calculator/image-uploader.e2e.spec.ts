import { test, expect } from '@playwright/test'
import { createTestUser, getAuthToken } from '../utils/auth'
import { TEST_CONFIG } from '../utils/calculator.utils'
import { seedDemoImages } from '../utils/setup'
import path from 'path'
import fs from 'fs'
import { fileURLToPath } from 'url'

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const rootDir = path.resolve(__dirname, '../..')

// Ensure test-results directory exists
const screenshotDir = path.join(rootDir, 'test-results')
if (!fs.existsSync(screenshotDir)) {
  fs.mkdirSync(screenshotDir, { recursive: true })
}

// Test users configuration
const TEST_USERS = {
  ADMIN: {
    email: '<EMAIL>',
    password: 'admin',
    roles: ['super-admin'] as ('super-admin' | 'user')[],
  },
} as const

// Selectors used across tests
const SELECTORS = {
  UPLOAD_HEADING: 'Upload Your Design',
  CALCULATOR_HEADING: 'Embroidery Size Calculator',
  IMAGE_INPUT: 'image-input',
  CALCULATOR_RESULT: 'calculator-result',
  DEMO_IMAGE_BUTTON: 'demo-image-button',
  CHOOSE_FILE_BUTTON: 'button:has-text("Choose File")',
} as const

// Common test actions
const navigateToCalculator = async (page) => {
  await page.goto('/calculator', { timeout: TEST_CONFIG.CANVAS_TIMEOUT })
  await Promise.all([
    page.waitForLoadState('networkidle'),
    page.waitForLoadState('domcontentloaded'),
    page
      .getByRole('heading', { name: SELECTORS.CALCULATOR_HEADING })
      .waitFor({ timeout: TEST_CONFIG.CANVAS_TIMEOUT }),
    page
      .getByRole('heading', { name: SELECTORS.UPLOAD_HEADING })
      .waitFor({ timeout: TEST_CONFIG.CANVAS_TIMEOUT }),
  ])
}

test.describe('Image Uploader', () => {
  // Set timeout for all tests in this describe block
  test.setTimeout(TEST_CONFIG.BACKGROUND_REMOVAL_TIMEOUT)

  test.beforeAll(async () => {
    // Create test admin user and seed demo images
    await Promise.all([createTestUser(TEST_USERS.ADMIN), seedDemoImages()])
  })

  test.beforeEach(async ({ page }) => {
    // Enable detailed console logging for debugging
    page.on('console', (msg) => {
      const text = msg.text()
      const type = msg.type()
      console.log(`Browser ${type}: ${text}`)
    })
  })

  test('shows locked state for non-authenticated users', async ({ page }) => {
    await navigateToCalculator(page)

    const uploadHeading = page.getByRole('heading', { name: SELECTORS.UPLOAD_HEADING })
    const fileInput = page.getByTestId(SELECTORS.IMAGE_INPUT)

    await expect(uploadHeading).toBeVisible()
    await expect(page.getByRole('button', { name: 'Locked' })).toBeVisible()
    await expect(
      page.getByText('Please subscribe to our unlimited plan or purchase credits'),
    ).toBeVisible()
    await expect(fileInput).toBeDisabled()
  })

  test('allows uploads for authenticated admin users', async ({ context, browser }) => {
    console.log('Starting authentication test')

    // Get a real authentication token via API
    console.log('Getting auth token')
    const token = await getAuthToken()
    console.log('Received token:', token ? 'Token obtained successfully' : 'No token received')

    if (!token) {
      throw new Error('Failed to get authentication token')
    }

    // Create a new context with the authentication token
    const newContext = await browser.newContext({
      storageState: {
        cookies: [
          {
            name: 'payload-token',
            value: token,
            domain: 'localhost',
            path: '/',
            expires: Math.floor(Date.now() / 1000) + 86400, // 24 hour expiry
            httpOnly: false,
            secure: false,
            sameSite: 'Lax' as const,
          },
        ],
        origins: [],
      },
    })

    // Create a new page in the authenticated context
    console.log('Creating authenticated page')
    const page = await newContext.newPage()

    try {
      // Navigate to calculator
      console.log('Navigating to calculator')
      await navigateToCalculator(page)

      // Wait a moment for auth state to be processed
      console.log('Waiting for auth to be processed')
      await page.waitForTimeout(2000)

      // Check for Choose File button (only visible when authenticated)
      console.log('Checking for Choose File button')
      await page.waitForSelector(SELECTORS.CHOOSE_FILE_BUTTON, { timeout: 10000 })
      const chooseFileButton = await page.$(SELECTORS.CHOOSE_FILE_BUTTON)

      if (!chooseFileButton) {
        // Take a screenshot if button not found
        await page.screenshot({ path: path.join(screenshotDir, 'debug-auth-failed.png') })
        throw new Error('Choose File button not found - auth may have failed')
      }

      console.log('Choose File button found')

      // Verify file input is enabled
      const fileInput = page.getByTestId(SELECTORS.IMAGE_INPUT)
      const isDisabled = await fileInput.getAttribute('disabled')
      console.log('File input disabled attribute:', isDisabled)

      // Need to check existence rather than enabled state since the input might be hidden
      expect(isDisabled).toBeNull()

      console.log('Test completed successfully')
    } finally {
      await newContext.close()
    }
  })

  test('handles demo images for all users', async ({ page }) => {
    await navigateToCalculator(page)

    // Verify demo images section
    const demoSection = page.getByText('Try a Demo Design')
    await expect(demoSection).toBeVisible()
    await expect(page.getByText('Select from our collection of sample designs')).toBeVisible()

    // Wait for and click first demo image
    const demoButton = page.getByTestId(SELECTORS.DEMO_IMAGE_BUTTON).first()
    await expect(demoButton).toBeVisible({
      timeout: TEST_CONFIG.BACKGROUND_REMOVAL_TIMEOUT,
    })
    await demoButton.click()

    // Verify result
    await expect(page.getByTestId(SELECTORS.CALCULATOR_RESULT)).toBeVisible({
      timeout: TEST_CONFIG.BACKGROUND_REMOVAL_TIMEOUT,
    })
  })
})
