import { test, expect } from '@playwright/test'
import {
  TEST_CONFIG,
  waitForCanvasAndImage,
  verifyCalculatorControls,
  uploadImageAndWaitForDialog,
  verifyImageCentering,
} from '../utils/calculator.utils'

test.describe('Image Upload and Background Handling', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to calculator page and ensure it's fully loaded
    await page.goto('/calculator')
    await Promise.all([
      page.waitForLoadState('networkidle'),
      page.waitForLoadState('domcontentloaded'),
    ])

    // Verify calculator page is loaded
    const pageTitle = page.getByRole('heading', { name: 'Embroidery Size Calculator' })
    await expect(pageTitle).toBeVisible({ timeout: TEST_CONFIG.DEFAULT_WAIT_TIME })

    // Log browser console messages for debugging
    page.on('console', (msg) => {
      const type = msg.type()
      const text = msg.text()
      if (type === 'error') {
        console.error(`Browser console error: ${text}`)
      } else {
        console.log(`Browser console ${type}: ${text}`)
      }
    })
  })

  test('should keep image background when selected', async ({ page }) => {
    // Upload image and wait for background dialog
    const backgroundDialog = await uploadImageAndWaitForDialog(page, TEST_CONFIG.TEST_IMAGE_PATH)
    await expect(backgroundDialog).toBeVisible()

    // Verify background options are available
    const keepBackgroundButton = page.getByRole('button', { name: 'Keep Background' })
    await expect(keepBackgroundButton).toBeVisible()

    // Keep background and verify dialog is dismissed
    await keepBackgroundButton.click()
    await expect(backgroundDialog).not.toBeVisible()

    // Verify image is loaded and centered correctly
    await waitForCanvasAndImage(page)
    await verifyCalculatorControls(page)
    await verifyImageCentering(page)
  })

  test('should remove image background when selected', async ({ page }) => {
    // Set longer timeout for background removal process
    test.setTimeout(TEST_CONFIG.BACKGROUND_REMOVAL_TIMEOUT)

    // Upload image and wait for background dialog
    const backgroundDialog = await uploadImageAndWaitForDialog(page, TEST_CONFIG.TEST_IMAGE_PATH)
    await expect(backgroundDialog).toBeVisible()

    // Verify and click remove background option
    const removeBackgroundButton = page.getByRole('button', { name: 'Remove Background' })
    await expect(removeBackgroundButton).toBeVisible()
    await removeBackgroundButton.click()

    // Verify loading state and completion
    const loadingSpinner = page.locator('[role="progressbar"]')
    await expect(loadingSpinner).toBeVisible()

    // Wait for background removal to complete
    await expect(backgroundDialog).not.toBeVisible({
      timeout: TEST_CONFIG.BACKGROUND_REMOVAL_TIMEOUT,
    })

    // Verify final state
    await waitForCanvasAndImage(page, TEST_CONFIG.CANVAS_TIMEOUT)
    await verifyCalculatorControls(page)
  })
})
