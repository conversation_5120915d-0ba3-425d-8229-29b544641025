import { test as base, expect } from '@playwright/test'
import { TEST_CONFIG } from '../utils/calculator.utils'
import { loginTestUser, createTestUser } from '../utils/auth'
import path from 'path'
import fs from 'fs'

// Define a test that handles downloads in both headed and headless modes
const test = base.extend({
  // Add auto-download handling for headed mode
  context: async ({ context }, runTest) => {
    // Configure download behavior
    context.setDefaultTimeout(60000) // Increase timeout for downloads

    // Use the configured context
    await runTest(context)
  },
})

test.describe('PDF Report Generation', () => {
  test.beforeAll(async () => {
    // Create test user with unlimited plan
    await createTestUser({
      email: '<EMAIL>',
      password: 'password',
      subscription_status: 'unlimited',
    })
  })

  async function waitForPageLoad(page: any) {
    try {
      await Promise.all([
        page.waitForLoadState('networkidle'),
        page.waitForLoadState('domcontentloaded'),
        page.getByRole('heading', { name: 'Embroidery Size Calculator', level: 1 }).waitFor({
          timeout: TEST_CONFIG.CANVAS_TIMEOUT,
          state: 'visible',
        }),
      ])
    } catch (error) {
      console.error('Failed to load page:', error)
      await page.screenshot({ path: '.test-artifacts/page-load-error.png' })
      throw error
    }
  }

  test('generates PDF report from demo image', async ({ page, context }) => {
    // Login as unlimited user
    await loginTestUser(page, '<EMAIL>', 'password', {
      redirectUrl: '/calculator',
    })

    // Wait for page load after login
    await waitForPageLoad(page)

    // Click on a demo image
    const demoSection = page.getByRole('heading', { name: 'Try a Demo Design' })
    await expect(demoSection).toBeVisible({ timeout: TEST_CONFIG.CANVAS_TIMEOUT })

    // Find and click the first demo image
    const demoImages = page.getByTestId('demo-image-button')
    await expect(demoImages.first()).toBeVisible({ timeout: TEST_CONFIG.CANVAS_TIMEOUT })
    await demoImages.first().click()

    // Wait for the image to load and process
    await page.waitForTimeout(TEST_CONFIG.DEFAULT_WAIT_TIME * 2)

    // Check if there's a background removal dialog and handle it
    const backgroundDialog = page.getByRole('dialog')
    if (await backgroundDialog.isVisible({ timeout: 5000 }).catch(() => false)) {
      console.log('Background removal dialog detected, handling it...')
      const keepBackgroundButton = page.getByRole('button', { name: 'Keep Background' })
      if (await keepBackgroundButton.isVisible({ timeout: 5000 }).catch(() => false)) {
        await keepBackgroundButton.click()
        await page.waitForTimeout(TEST_CONFIG.DEFAULT_WAIT_TIME)
      }
    }

    // Click the Generate Estimate button
    const generateEstimateButton = page.getByTestId('generate-estimate-button')
    await expect(generateEstimateButton).toBeVisible({ timeout: TEST_CONFIG.CANVAS_TIMEOUT })
    await generateEstimateButton.click()

    // Wait for the analysis to complete
    const stitchCountResult = page.getByTestId('stitch-count-result')
    await expect(stitchCountResult).toBeVisible({ timeout: TEST_CONFIG.CANVAS_TIMEOUT })

    // Configure browser for downloads (different handling in headed vs headless mode)
    try {
      // This works in Chromium headed mode
      if (process.env.HEADED === '1') {
        await context.grantPermissions(['downloads'])
      }
    } catch (_) {
      console.log('Note: Download permissions not needed in headless mode')
    }

    // Set up download listener before clicking the button
    const downloadPromise = page.waitForEvent('download', { timeout: 30000 })

    // Click the generate PDF button
    const generatePdfButton = page.getByTestId('generate-pdf-button')
    await expect(generatePdfButton).toBeVisible({ timeout: TEST_CONFIG.DEFAULT_WAIT_TIME })
    await generatePdfButton.click()

    // Wait for the button to show loading state
    await expect(page.getByText('Generating PDF...')).toBeVisible({
      timeout: TEST_CONFIG.DEFAULT_WAIT_TIME,
    })

    // Wait for the download to start
    const download = await downloadPromise
    console.log('Download started:', download.suggestedFilename())

    // In headed mode, the download might be handled by the browser's download manager
    // We'll use a more robust approach that works in both headed and headless modes

    // Create a safe path for the download
    const downloadFolder = path.join(process.cwd(), '.test-artifacts')
    if (!fs.existsSync(downloadFolder)) {
      fs.mkdirSync(downloadFolder, { recursive: true })
    }

    const downloadPath = path.join(downloadFolder, download.suggestedFilename())

    // Save the download to our specified path
    await download.saveAs(downloadPath)
    console.log('Download saved to:', downloadPath)

    // Verify the file exists and has content
    await page.waitForTimeout(1000) // Give the file system a moment
    const fileExists = fs.existsSync(downloadPath)
    expect(fileExists).toBe(true)
    if (!fileExists) console.error(`File does not exist at ${downloadPath}`)

    const fileStats = fs.statSync(downloadPath)
    const fileHasContent = fileStats.size > 0
    expect(fileHasContent).toBe(true)
    if (!fileHasContent) console.error('File is empty')

    // Verify the file has a PDF extension
    expect(path.extname(download.suggestedFilename())).toBe('.pdf')

    // Verify the file was downloaded successfully
    // We'll skip the toast verification since it might be flaky in the test environment
    console.log('PDF downloaded successfully:', download.suggestedFilename())
  })
})
