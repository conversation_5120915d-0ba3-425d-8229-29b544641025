import { test, expect, Page } from '@playwright/test'
import { TEST_CONFIG } from '../utils/calculator.utils'

/**
 * Helper to retrieve the canvas zoom level
 */
async function getCanvasZoom(page: Page): Promise<number | null> {
  try {
    const zoom = await page.evaluate(() => {
      const canvas = document.querySelector('[data-testid="canvas"]') as HTMLElement
      if (!canvas) {
        console.error('Canvas element not found')
        return null
      }

      const canvasState = JSON.parse(canvas.getAttribute('data-canvas-state') || '{}')
      return canvasState.zoom || null
    })

    return zoom
  } catch (error) {
    console.error('Error getting canvas zoom:', error)
    return null
  }
}

/**
 * Helper to upload a test image and handle background detection
 */
async function uploadTestImage(page: Page): Promise<void> {
  const testImagePath = TEST_CONFIG.TEST_IMAGE_PATH
  console.log(`Uploading test image from: ${testImagePath}`)

  // Set input files
  console.log('Setting input files...')
  const fileInput = page.locator('input[type="file"]')
  await fileInput.setInputFiles(testImagePath)

  // Wait for processing to start
  console.log('Waiting for upload to process...')

  // Handle background detection
  await handleBackgroundDetection(page)
}

/**
 * Helper function to check if any dialog or modal is open
 */
async function ensureNoDialogsOpen(page: Page): Promise<void> {
  console.log('Checking for any open dialogs or modals...')

  // List of common dialog/modal selectors
  const dialogSelectors = [
    '[data-testid="background-detection-dialog"]',
    '[role="dialog"]',
    '[data-state="open"]',
    '.fixed.inset-0.z-50', // Common overlay pattern in Tailwind
  ]

  for (const selector of dialogSelectors) {
    const elements = page.locator(selector)
    const count = await elements.count()

    if (count > 0) {
      console.log(`Found ${count} elements matching "${selector}"`)

      // Try to find a close button or press Escape
      try {
        // Look for close buttons
        const closeButtons = page.locator(
          'button[aria-label="Close"], [data-testid="close-button"], .close-button',
        )
        const closeButtonCount = await closeButtons.count()

        if (closeButtonCount > 0) {
          console.log(`Found ${closeButtonCount} close buttons, clicking the first one...`)
          await closeButtons.first().click()
          console.log('Clicked close button')
        } else {
          // Try pressing Escape
          console.log('No close button found, pressing Escape key...')
          await page.keyboard.press('Escape')
          console.log('Pressed Escape key')
        }

        // Wait to see if the dialog closes
        await page.waitForTimeout(1000)
      } catch (error) {
        console.error('Error trying to close dialog:', error)
      }
    }
  }

  // Final check for any remaining dialogs
  let anyRemainingDialogs = false
  for (const selector of dialogSelectors) {
    const count = await page.locator(selector).count()
    if (count > 0) {
      console.log(`Still found ${count} elements matching "${selector}" after attempting to close`)
      anyRemainingDialogs = true
    }
  }

  if (anyRemainingDialogs) {
    console.warn('WARNING: There are still dialogs open that may block interactions')
  } else {
    console.log('No open dialogs found, proceeding with test')
  }
}

/**
 * Helper to handle the background detection dialog
 */
async function handleBackgroundDetection(page: Page): Promise<void> {
  // Try to find the dialog
  console.log('Looking for background dialog...')
  let dialogFound = false

  for (let attempt = 1; attempt <= 3; attempt++) {
    console.log(`Attempt ${attempt} to find background dialog...`)
    try {
      // Wait for the dialog to appear with a timeout
      const dialog = page.locator('[data-testid="background-detection-dialog"]')

      // Wait for dialog to be visible
      const isVisible = await dialog.isVisible({ timeout: TEST_CONFIG.DEFAULT_WAIT_TIME })

      if (isVisible) {
        dialogFound = true
        console.log('Background dialog found!')

        // Check dialog content
        console.log('Checking dialog title and content...')
        await expect(page.locator('[data-testid="background-detection-title"]')).toBeVisible()

        // Look for and click the Keep Background button
        console.log('Looking for Keep Background button...')
        const keepBackgroundButton = page.locator('[data-testid="keep-background-button"]')
        await expect(keepBackgroundButton).toBeVisible()

        console.log('Clicking Keep Background button...')
        await keepBackgroundButton.click()

        // Wait for dialog to close
        console.log('Waiting for dialog to close...')
        await expect(dialog).not.toBeVisible({ timeout: TEST_CONFIG.DEFAULT_WAIT_TIME })
        console.log('Dialog closed successfully')

        // Wait extra time for any animation to complete
        await page.waitForTimeout(TEST_CONFIG.DEFAULT_WAIT_TIME)

        break
      }
    } catch (error) {
      console.log(`Attempt ${attempt} to find dialog failed:`, error)

      if (attempt === 3) {
        console.log('Background dialog not found after multiple attempts, continuing...')
      } else {
        // Wait before next attempt
        await page.waitForTimeout(TEST_CONFIG.DEFAULT_WAIT_TIME / 2)
      }
    }
  }

  // If no dialog was found, check if image preview is visible instead
  if (!dialogFound) {
    console.log('No background dialog found, checking if image is already visible...')
    await page.waitForSelector('[data-testid="image-preview"]', {
      state: 'visible',
      timeout: TEST_CONFIG.CANVAS_TIMEOUT,
    })
  }

  // Ensure no other dialogs are open
  await ensureNoDialogsOpen(page)

  // Wait for image preview to be visible
  console.log('Waiting for image preview to be visible...')
  await page.waitForSelector('[data-testid="image-preview"]', {
    state: 'visible',
    timeout: TEST_CONFIG.CANVAS_TIMEOUT,
  })
  console.log('Image preview is visible')

  // Wait for image to fully load
  console.log('Waiting for image to fully load...')
  await page.waitForTimeout(TEST_CONFIG.DEFAULT_WAIT_TIME)

  // Ensure again no dialogs are open before proceeding
  await ensureNoDialogsOpen(page)
}

test.describe('Fit to Canvas Test', () => {
  test('should fit image content to canvas', async ({ page }) => {
    let currentStage = 'starting'
    try {
      console.log('[Stage: starting] Beginning fit-to-canvas test...')
      // Go to calculator page
      await page.goto('/calculator')
      currentStage = 'navigation-complete'
      console.log('[Stage: navigation-complete] Navigated to calculator page')

      // Enable verbose console logging for debugging
      page.on('console', (msg) => console.log(`Browser console: ${msg.text()}`))
      page.on('pageerror', (err) => console.error(`Browser page error: ${err.message}`))

      // Upload image directly using the more robust helper
      currentStage = 'upload-starting'
      console.log('[Stage: upload-starting] Uploading test image...')
      try {
        await uploadTestImage(page)
        currentStage = 'upload-complete'
        console.log('[Stage: upload-complete] Test image uploaded successfully')
      } catch (error) {
        console.error(`[Stage: ${currentStage}] Error uploading test image:`, error)
        // Take a screenshot for debugging
        await page.screenshot({ path: 'test-results/upload-error.png' })
        throw error
      }

      // Explicitly wait for canvas to stabilize
      currentStage = 'canvas-stabilizing'
      console.log('[Stage: canvas-stabilizing] Waiting for canvas to stabilize...')
      await page.waitForTimeout(TEST_CONFIG.DEFAULT_WAIT_TIME)

      // Ensure no dialogs are open before attempting to use zoom controls
      currentStage = 'ensuring-no-dialogs'
      console.log(
        '[Stage: ensuring-no-dialogs] Making sure no dialogs are blocking interactions...',
      )
      await ensureNoDialogsOpen(page)

      // Check if zoom controls are available with retries
      currentStage = 'checking-zoom-controls'
      console.log('[Stage: checking-zoom-controls] Looking for zoom controls...')

      let controlsFound = false
      let zoomInButton, fitToCanvasButton

      for (let attempt = 1; attempt <= 5; attempt++) {
        try {
          console.log(`[Stage: checking-zoom-controls] Attempt ${attempt} to find zoom controls...`)
          zoomInButton = page.locator('[data-testid="zoom-in"]')
          fitToCanvasButton = page.locator('[data-testid="fit-to-canvas"]')

          const zoomInVisible = await zoomInButton.isVisible({
            timeout: TEST_CONFIG.DEFAULT_WAIT_TIME / 2,
          })
          const fitToCanvasVisible = await fitToCanvasButton.isVisible({
            timeout: TEST_CONFIG.DEFAULT_WAIT_TIME / 2,
          })

          if (zoomInVisible && fitToCanvasVisible) {
            controlsFound = true
            console.log(`[Stage: checking-zoom-controls] Zoom controls found on attempt ${attempt}`)
            break
          }

          console.log(
            `[Stage: checking-zoom-controls] Controls not found on attempt ${attempt}, waiting...`,
          )
          await page.waitForTimeout(TEST_CONFIG.DEFAULT_WAIT_TIME / 2)
        } catch (error) {
          console.error(
            `[Stage: checking-zoom-controls] Error finding controls (attempt ${attempt}):`,
            error,
          )

          // Take a screenshot on last attempt
          if (attempt === 5) {
            await page.screenshot({ path: 'test-results/zoom-controls-not-found.png' })
          }

          // Wait before next attempt
          await page.waitForTimeout(TEST_CONFIG.DEFAULT_WAIT_TIME / 2)
        }
      }

      if (!controlsFound) {
        throw new Error('Could not find zoom controls after multiple attempts')
      }

      currentStage = 'zoom-controls-found'
      console.log('[Stage: zoom-controls-found] Zoom controls are visible')

      // Force-click using page.evaluate to bypass any potential overlays
      currentStage = 'zooming-in'
      console.log('[Stage: zooming-in] Force-clicking zoom in button via JavaScript...')
      await page.evaluate(() => {
        const zoomButton = document.querySelector('[data-testid="zoom-in"]') as HTMLElement
        if (zoomButton) {
          console.log('Found zoom button in DOM, clicking programmatically')
          zoomButton.click()
          return true
        } else {
          console.error('Could not find zoom button in DOM')
          return false
        }
      })

      await page.waitForTimeout(1000)

      // Force-click fit to canvas as well
      currentStage = 'fitting-to-canvas'
      console.log(
        '[Stage: fitting-to-canvas] Force-clicking fit to canvas button via JavaScript...',
      )
      await page.evaluate(() => {
        const fitButton = document.querySelector('[data-testid="fit-to-canvas"]') as HTMLElement
        if (fitButton) {
          console.log('Found fit-to-canvas button in DOM, clicking programmatically')
          fitButton.click()
          return true
        } else {
          console.error('Could not find fit-to-canvas button in DOM')
          return false
        }
      })

      // Wait for fit animation to complete
      await page.waitForTimeout(1500)

      // Take a screenshot to debug
      currentStage = 'post-fit-screenshot'
      console.log('[Stage: post-fit-screenshot] Taking screenshot of fit result...')
      await page.screenshot({ path: 'test-results/fit-to-canvas-result.png' })

      // Success - we've reached the end of the test
      currentStage = 'test-complete'
      console.log('[Stage: test-complete] Test completed successfully')
    } catch (error) {
      console.error(`[Stage: ${currentStage}] Test failed at stage ${currentStage}:`, error)
      // Take a screenshot to show the current state
      try {
        await page.screenshot({ path: `test-results/failure-at-${currentStage}.png` })
      } catch (screenshotError) {
        console.error('Could not take failure screenshot:', screenshotError)
      }
      throw error
    }
  })
})
