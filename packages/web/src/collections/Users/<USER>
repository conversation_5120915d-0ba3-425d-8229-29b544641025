import type { CollectionConfig, PayloadRequest } from 'payload'
import { isSuperAdmin } from '../../access/isSuperAdmin'
import {
  anyoneCanCreateAccount,
  usersCanEditOwnAdminsEditAll,
  usersCanDeleteOwnAdminsDeleteAll,
} from './access'
import { usersCanReadOwnAdminsReadAll } from '@/access/usersCanReadOwnAdminsReadAll'

export const Users: CollectionConfig = {
  slug: 'users',
  access: {
    admin: isSuperAdmin as ({ req }: { req: PayloadRequest }) => boolean | Promise<boolean>,
    create: anyoneCanCreateAccount,
    read: usersCanReadOwnAdminsReadAll,
    update: usersCanEditOwnAdminsEditAll,
    delete: usersCanDeleteOwnAdminsDeleteAll,
  },
  admin: {
    defaultColumns: ['name', 'email'],
    useAsTitle: 'name',
  },
  auth: true,
  fields: [
    {
      name: 'name',
      type: 'text',
    },
    {
      name: 'roles',
      type: 'select',
      defaultValue: ['user'],
      hasMany: true,
      options: ['super-admin', 'user'],
      access: {
        update: ({ req: { user } }) => {
          if (!user) return false
          if ('collection' in user && user.collection === 'api-tokens') return false
          return Boolean(user.roles?.includes('super-admin'))
        },
      },
    },
    {
      name: 'paddle_customer_id',
      type: 'text',
      admin: {
        description: 'Paddle Customer ID for subscription management',
      },
      index: true,
      unique: true,
    },
    {
      name: 'credits',
      type: 'number',
      defaultValue: 0,
      admin: {
        description: 'Available credits for pay-as-you-go usage',
      },
    },
    {
      name: 'subscription_status',
      type: 'select',
      options: ['none', 'unlimited'],
      defaultValue: 'none',
      admin: {
        description: 'User subscription status',
      },
    },
  ],
  timestamps: true,
}
