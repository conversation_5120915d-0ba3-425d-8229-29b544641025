'use client'

import { useEffect, useRef, useState, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog'
import {
  Download,
  ZoomIn,
  ZoomOut,
  Maximize,
  Settings2,
  AlertTriangle,
  Loader2,
} from 'lucide-react'
import { processEmbroideryFile } from '@stitchestimate/embroidery-processor'
import { EmbroideryFileMetadata, IPattern } from '@/types/embroidery-shared'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { fabric } from 'fabric'
import { renderEmbroideryPatternPaths } from './FabricEmbroideryViewerUtils'
// Import stitchTypes when needed: import { stitchTypes } from '@stitchestimate/embroidery-processor'

interface FabricEmbroideryViewerProps {
  file?: File
  globalLineWidth?: number
  globalUse3dEffect?: boolean
  globalResolutionScale?: number
  onMetadataChange?: (metadata: EmbroideryFileMetadata | null) => void
  isPreview?: boolean
  showControls?: boolean
}

// Type for our custom canvas
interface CustomCanvas extends fabric.Canvas {
  isDragging?: boolean
  lastPosX?: number
  lastPosY?: number
}

export default function FabricEmbroideryViewer({
  file,
  globalLineWidth = 1,
  globalUse3dEffect = false,
  globalResolutionScale = 1,
  onMetadataChange,
  isPreview = false,
  showControls = true,
}: FabricEmbroideryViewerProps) {
  const canvasContainerRef = useRef<HTMLDivElement>(null)
  const fabricCanvasRef = useRef<CustomCanvas | null>(null)
  const [metadata, setMetadata] = useState<EmbroideryFileMetadata | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [localLineWidth, setLocalLineWidth] = useState<number | null>(null)
  const [isConfigOpen, setIsConfigOpen] = useState(false)
  const [localUse3dEffect, setLocalUse3dEffect] = useState<boolean | null>(null)
  const [localResolutionScale, setLocalResolutionScale] = useState<number | null>(null)
  const [showJumpStitches, setShowJumpStitches] = useState<boolean>(false)
  const [processedFile, setProcessedFile] = useState<any>(null)
  const [isProcessing, setIsProcessing] = useState<boolean>(true)
  const [isRendering, setIsRendering] = useState<boolean>(false)
  const [_zoomLevel, setZoomLevel] = useState(1) // Prefix with underscore to indicate it's unused

  // Use a ref for the metadata change callback to avoid re-renders
  const metadataChangeHandled = useRef(false)

  // Add a ref to track whether this is the first render
  const initialRenderComplete = useRef(false)

  // Add a more detailed logging function
  const logCanvasInfo = useCallback((label: string) => {
    if (!fabricCanvasRef.current) return

    const canvas = fabricCanvasRef.current
    console.log(
      `[${label}] Canvas: ${canvas.width}x${canvas.height}, Container: ${canvasContainerRef.current?.clientWidth}x${canvasContainerRef.current?.clientHeight}, Zoom: ${canvas.getZoom()}`,
    )
  }, [])

  // Use local settings if set, otherwise fall back to global settings
  const effectiveLineWidth = localLineWidth ?? globalLineWidth
  const effectiveUse3dEffect = localUse3dEffect !== null ? localUse3dEffect : globalUse3dEffect
  const effectiveResolutionScale = localResolutionScale ?? globalResolutionScale

  // Render embroidery pattern using Fabric.js
  const renderEmbroideryPattern = useCallback(() => {
    if (!processedFile?.pattern || isProcessing || !fabricCanvasRef.current) return

    console.log('Starting fabric.js rendering...')
    setIsRendering(true)
    logCanvasInfo('pre-render')

    const pattern = processedFile.pattern
    const canvas = fabricCanvasRef.current

    // Clear canvas
    canvas.clear()

    // Ensure the pattern has a bounding box
    pattern.calculateBoundingBox()

    // Calculate dimensions and scaling
    const patternWidth = pattern.right - pattern.left
    const patternHeight = pattern.bottom - pattern.top

    console.log('Pattern dimensions:', {
      left: pattern.left,
      top: pattern.top,
      right: pattern.right,
      bottom: pattern.bottom,
      width: patternWidth,
      height: patternHeight,
    })

    // Check if the pattern has valid dimensions
    if (patternWidth <= 0 || patternHeight <= 0) {
      console.warn('Invalid pattern dimensions, using default values')
      pattern.left = -18.1
      pattern.top = -18.1
      pattern.right = 18.2
      pattern.bottom = 18.1
    }

    const canvasWidth = canvas.width!
    const canvasHeight = canvas.height!

    // Calculate scale to fit pattern in canvas with proportional padding
    // Use smaller padding for more fitting (reduce from 8% to 5%)
    const paddingPercent = 0.05
    const padding = Math.min(canvasWidth, canvasHeight) * paddingPercent

    // Ensure we have valid dimensions to avoid division by zero
    const validWidth = patternWidth > 0 ? patternWidth : 10
    const validHeight = patternHeight > 0 ? patternHeight : 10

    const scaleX = (canvasWidth - padding * 2) / validWidth
    const scaleY = (canvasHeight - padding * 2) / validHeight
    const scale = Math.min(scaleX, scaleY)

    console.log('Canvas dimensions:', { width: canvasWidth, height: canvasHeight, scale, padding })

    // Offset to center the pattern
    const offsetX = (canvasWidth - patternWidth * scale) / 2
    const offsetY = (canvasHeight - patternHeight * scale) / 2

    // Group all paths for better performance
    const patternGroup = new fabric.Group([], {
      left: 0,
      top: 0,
      selectable: false,
      hoverCursor: 'default',
    })

    // Track colors used
    const colorPaths: Record<number, fabric.Path[]> = {}

    // Initialize color paths array
    pattern.colors.forEach((_, index) => {
      colorPaths[index] = []
    })

    // Use the extracted utility function to generate paths
    renderEmbroideryPatternPaths(
      pattern,
      scale,
      offsetX,
      offsetY,
      createAndAddPath,
      colorPaths,
      canvas,
      showJumpStitches,
    )

    // Helper function to create and add paths
    function createAndAddPath(points: { x: number; y: number }[], colorIndex: number) {
      if (points.length < 2) {
        console.log(`Skipping path with only ${points.length} points`)
        return
      }

      // Simplify the path by removing redundant points
      const simplifiedPoints: { x: number; y: number }[] = []
      for (let i = 0; i < points.length; i++) {
        // Skip points that are too close to the previous point
        if (i > 0) {
          const prevPoint = simplifiedPoints[simplifiedPoints.length - 1]
          const dx = points[i].x - prevPoint.x
          const dy = points[i].y - prevPoint.y
          const distance = Math.sqrt(dx * dx + dy * dy)

          // Skip points that are too close (less than 1 pixel apart)
          if (distance < 1) {
            continue
          }
        }

        simplifiedPoints.push(points[i])
      }

      // If after simplification we have fewer than 2 points, skip this path
      if (simplifiedPoints.length < 2) {
        console.log(
          `Skipping path with only ${simplifiedPoints.length} points after simplification`,
        )
        return
      }

      console.log(`Simplified path from ${points.length} to ${simplifiedPoints.length} points`)

      const color = pattern.colors[colorIndex]
      if (!color) {
        console.log(`No color found for index ${colorIndex}, using default black`)
        // Use default black if no color is found
        const colorRgb = 'rgb(0, 0, 0)'

        // Convert points to SVG path
        let pathData = `M ${simplifiedPoints[0].x} ${simplifiedPoints[0].y}`
        for (let i = 1; i < simplifiedPoints.length; i++) {
          pathData += ` L ${simplifiedPoints[i].x} ${simplifiedPoints[i].y}`
        }

        // Create path object
        const path = new fabric.Path(pathData, {
          stroke: colorRgb,
          strokeWidth: effectiveLineWidth,
          fill: '',
          strokeLineCap: 'round',
          strokeLineJoin: 'round',
        })

        // Add to color paths and pattern group
        if (!colorPaths[0]) colorPaths[0] = []
        colorPaths[0].push(path)
        patternGroup.addWithUpdate(path)

        return
      }

      const colorRgb = `rgb(${color.r}, ${color.g}, ${color.b})`
      console.log(
        `Creating path with ${simplifiedPoints.length} points using color ${colorRgb} ${colorIndex}`,
      )

      // Convert points to SVG path
      let pathData = `M ${simplifiedPoints[0].x} ${simplifiedPoints[0].y}`
      for (let i = 1; i < simplifiedPoints.length; i++) {
        pathData += ` L ${simplifiedPoints[i].x} ${simplifiedPoints[i].y}`
      }

      // Create path object
      const path = new fabric.Path(pathData, {
        stroke: colorRgb,
        strokeWidth: effectiveLineWidth,
        fill: '',
        strokeLineCap: 'round',
        strokeLineJoin: 'round',
      })

      // Make sure the color path array exists
      if (!colorPaths[colorIndex]) {
        colorPaths[colorIndex] = []
      }

      // Add shadow if 3D effect is enabled
      if (effectiveUse3dEffect) {
        path.set({
          shadow: new fabric.Shadow({
            color: colorRgb,
            blur: 4,
            offsetX: 1,
            offsetY: 1,
          }),
        })
      }

      colorPaths[colorIndex].push(path)
      patternGroup.addWithUpdate(path)
    }

    // Add all paths to canvas
    canvas.add(patternGroup)
    console.log(`Added ${Object.values(colorPaths).flat().length} paths to canvas`)

    // Reset view and render
    canvas.setViewportTransform([1, 0, 0, 1, 0, 0])
    canvas.renderAll()

    // After rendering, check if the object properly fits the canvas
    logCanvasInfo('post-render')

    // Set rendering to false when complete
    setTimeout(() => {
      setIsRendering(false)
      logCanvasInfo('render-complete')
    }, 200)
  }, [
    processedFile,
    isProcessing,
    effectiveLineWidth,
    effectiveUse3dEffect,
    logCanvasInfo,
    showJumpStitches,
  ])

  const handleEmbroideryFile = useCallback(async (file: File) => {
    if (!file) return

    setIsLoading(true)
    setError(null)
    setIsProcessing(true)
    metadataChangeHandled.current = false

    try {
      const result = await processEmbroideryFile(file)
      console.log('Processed file result:', result)

      // Ensure the returned result has the expected structure
      if (!result || !result.metadata) {
        throw new Error('Invalid file processing result structure')
      }

      setMetadata(result.metadata)
      setProcessedFile(result)
    } catch (err: any) {
      console.error('Error processing embroidery file:', err)
      setError(err.message || 'Failed to process embroidery file')
    } finally {
      setIsLoading(false)
      setIsProcessing(false)
    }
  }, [])

  useEffect(() => {
    if (file) {
      handleEmbroideryFile(file)
    }
  }, [file, handleEmbroideryFile])

  // Initialize Fabric.js canvas
  useEffect(() => {
    if (!canvasContainerRef.current) return

    console.log('Initializing fabric.js canvas...')
    // Force a reflow to get accurate dimensions
    const containerWidth = canvasContainerRef.current.clientWidth || 500
    const containerHeight = canvasContainerRef.current.clientHeight || 400

    console.log('Container dimensions:', { width: containerWidth, height: containerHeight })

    // Create canvas element
    const canvasElement = document.createElement('canvas')
    canvasElement.width = containerWidth
    canvasElement.height = containerHeight
    canvasElement.style.width = '100%'
    canvasElement.style.height = '100%'

    // Clear previous canvas
    if (canvasContainerRef.current.firstChild) {
      canvasContainerRef.current.innerHTML = ''
    }

    canvasContainerRef.current.appendChild(canvasElement)

    try {
      // Initialize Fabric canvas
      const canvas = new fabric.Canvas(canvasElement, {
        selection: false,
        renderOnAddRemove: true,
        width: containerWidth,
        height: containerHeight,
      }) as CustomCanvas

      // Initialize custom properties
      canvas.isDragging = false
      canvas.lastPosX = 0
      canvas.lastPosY = 0

      fabricCanvasRef.current = canvas

      console.log('Fabric canvas created:', {
        width: canvas.width,
        height: canvas.height,
        element: canvas.getElement(),
      })

      // Only add interactive handlers if not in preview mode
      if (!isPreview) {
        // Enable panning by default - no Alt key needed
        canvas.on('mouse:down', (opt) => {
          const evt = opt.e
          canvas.isDragging = true
          canvas.selection = false
          canvas.lastPosX = evt.clientX
          canvas.lastPosY = evt.clientY
        })

        canvas.on('mouse:move', (opt) => {
          if (canvas.isDragging) {
            const evt = opt.e
            const vpt = canvas.viewportTransform!
            vpt[4] += evt.clientX - (canvas.lastPosX || 0)
            vpt[5] += evt.clientY - (canvas.lastPosY || 0)
            canvas.requestRenderAll()
            canvas.lastPosX = evt.clientX
            canvas.lastPosY = evt.clientY
          }
        })

        canvas.on('mouse:up', () => {
          canvas.isDragging = false
          canvas.selection = true
        })

        // Enable zooming with mouse wheel
        canvas.on('mouse:wheel', (opt) => {
          const evt = opt.e
          evt.preventDefault()
          evt.stopPropagation()

          const delta = opt.e.deltaY
          let zoom = canvas.getZoom()
          zoom *= 0.999 ** delta

          // Limit zoom level
          if (zoom > 20) zoom = 20
          if (zoom < 0.1) zoom = 0.1

          const point = new fabric.Point(opt.e.offsetX, opt.e.offsetY)
          canvas.zoomToPoint(point, zoom)

          setZoomLevel(zoom)
        })
      }

      // We removed the old resize handler and ResizeObserver here
      // Now using our dedicated implementation instead

      return () => {
        canvas.dispose()
        fabricCanvasRef.current = null
      }
    } catch (err) {
      console.error('Error initializing fabric.js canvas:', err)
      setError('Failed to initialize the canvas. Please try refreshing the page.')
    }
  }, [isPreview])

  // Enhanced resize handler
  const handleCanvasResize = useCallback(
    (width: number, height: number) => {
      if (fabricCanvasRef.current && width > 0 && height > 0) {
        console.log('Resizing canvas to:', { width, height })

        fabricCanvasRef.current.setDimensions({
          width,
          height,
        })

        // Re-render the pattern after resize if it's available
        if (processedFile && !isProcessing) {
          // Use a delay to ensure canvas dimensions are fully updated
          setTimeout(() => {
            console.log('Re-rendering pattern after resize')
            // Reset the view before re-rendering to avoid scaling issues
            if (fabricCanvasRef.current) {
              fabricCanvasRef.current.setViewportTransform([1, 0, 0, 1, 0, 0])
              renderEmbroideryPattern()
            }
          }, 150) // Slightly longer delay for more reliable rendering
        }
      }
    },
    [processedFile, isProcessing, renderEmbroideryPattern],
  )

  // Use a ResizeObserver to track container size changes
  useEffect(() => {
    if (!canvasContainerRef.current) return

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        if (entry.target === canvasContainerRef.current) {
          const newWidth = entry.contentRect.width
          const newHeight = entry.contentRect.height

          // Only resize if dimensions have changed significantly
          if (
            Math.abs(newWidth - (fabricCanvasRef.current?.width || 0)) > 5 ||
            Math.abs(newHeight - (fabricCanvasRef.current?.height || 0)) > 5
          ) {
            console.log('Container resized significantly:', {
              width: newWidth,
              height: newHeight,
              canvasWidth: fabricCanvasRef.current?.width,
              canvasHeight: fabricCanvasRef.current?.height,
            })
            handleCanvasResize(newWidth, newHeight)
          }
        }
      }
    })

    if (canvasContainerRef.current) {
      resizeObserver.observe(canvasContainerRef.current)
    }

    return () => {
      resizeObserver.disconnect()
    }
  }, [handleCanvasResize])

  // Ensure we render again after the component is fully mounted
  useEffect(() => {
    if (
      fabricCanvasRef.current &&
      processedFile &&
      !isProcessing &&
      !initialRenderComplete.current
    ) {
      // This helps with rendering in modals, ensuring we get proper sizes
      setTimeout(() => {
        console.log('Performing post-mount render')
        if (fabricCanvasRef.current) {
          const width = canvasContainerRef.current?.clientWidth || 500
          const height = canvasContainerRef.current?.clientHeight || 400

          // Update canvas dimensions again to ensure they're correct
          fabricCanvasRef.current.setDimensions({ width, height })
          renderEmbroideryPattern()
          initialRenderComplete.current = true
        }
      }, 250) // Slightly longer delay for modal rendering
    }
  }, [processedFile, isProcessing, renderEmbroideryPattern])

  // Render pattern when processed file or settings change
  useEffect(() => {
    console.log('Pattern data or settings changed - rendering pattern')
    if (fabricCanvasRef.current && processedFile && !isProcessing) {
      // Render with a small delay to ensure the canvas is properly initialized
      setTimeout(() => renderEmbroideryPattern(), 50)
    }
  }, [
    processedFile,
    effectiveLineWidth,
    effectiveUse3dEffect,
    effectiveResolutionScale,
    showJumpStitches,
    renderEmbroideryPattern,
    isProcessing,
  ])

  // Handle zoom buttons
  const handleZoomIn = useCallback(() => {
    if (!fabricCanvasRef.current) return

    let zoom = fabricCanvasRef.current.getZoom()
    zoom = zoom * 1.2
    if (zoom > 20) zoom = 20

    const center = new fabric.Point(
      fabricCanvasRef.current.width! / 2,
      fabricCanvasRef.current.height! / 2,
    )

    fabricCanvasRef.current.zoomToPoint(center, zoom)
    setZoomLevel(zoom)
  }, [])

  const handleZoomOut = useCallback(() => {
    if (!fabricCanvasRef.current) return

    let zoom = fabricCanvasRef.current.getZoom()
    zoom = zoom / 1.2
    if (zoom < 0.1) zoom = 0.1

    const center = new fabric.Point(
      fabricCanvasRef.current.width! / 2,
      fabricCanvasRef.current.height! / 2,
    )

    fabricCanvasRef.current.zoomToPoint(center, zoom)
    setZoomLevel(zoom)
  }, [])

  const handleReset = useCallback(() => {
    if (!fabricCanvasRef.current) return

    fabricCanvasRef.current.setViewportTransform([1, 0, 0, 1, 0, 0])
    setZoomLevel(1)
    renderEmbroideryPattern()
  }, [renderEmbroideryPattern])

  const handleDownload = useCallback(() => {
    if (!fabricCanvasRef.current) return

    const dataURL = fabricCanvasRef.current.toDataURL({
      format: 'png',
      quality: 1,
      multiplier: effectiveResolutionScale,
    })

    const link = document.createElement('a')
    link.download = `embroidery-${file?.name || 'pattern'}.png`
    link.href = dataURL
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }, [effectiveResolutionScale, file?.name])

  // Notify parent component when metadata changes - only once per file
  useEffect(() => {
    if (metadata && onMetadataChange && !metadataChangeHandled.current) {
      onMetadataChange(metadata)
      metadataChangeHandled.current = true
    }
  }, [metadata, onMetadataChange])

  // Add event listener for external download triggers
  useEffect(() => {
    const downloadHandler = (event: Event) => {
      if (!file) return

      const customEvent = event as CustomEvent
      if (customEvent.detail?.fileName === file.name) {
        handleDownload()
      }
    }

    // Add event listener for external download triggers
    document.addEventListener('download-embroidery', downloadHandler)

    // Clean up
    return () => {
      document.removeEventListener('download-embroidery', downloadHandler)
    }
  }, [file, handleDownload])

  // Add event listener for external zoom and view controls
  useEffect(() => {
    const zoomHandler = (event: Event) => {
      if (!file) return

      const customEvent = event as CustomEvent
      if (customEvent.detail?.fileName === file.name) {
        const action = customEvent.detail?.action

        if (action === 'zoom-in') {
          handleZoomIn()
        } else if (action === 'zoom-out') {
          handleZoomOut()
        } else if (action === 'reset') {
          handleReset()
        }
      }
    }

    // Add event listener for external zoom controls
    document.addEventListener('zoom-embroidery', zoomHandler)

    // Clean up
    return () => {
      document.removeEventListener('zoom-embroidery', zoomHandler)
    }
  }, [file, handleZoomIn, handleZoomOut, handleReset])

  return (
    <div className="relative w-full h-full flex flex-col">
      {(isLoading || isProcessing || isRendering) && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-900/50 z-10 rounded-lg">
          <div className="flex flex-col items-center gap-2">
            <Loader2 className="w-8 h-8 animate-spin text-white" />
            <p className="text-sm font-medium text-white">
              {isProcessing
                ? 'Processing file...'
                : isRendering
                  ? 'Rendering pattern...'
                  : 'Loading...'}
            </p>
          </div>
        </div>
      )}

      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-900/50 z-10 rounded-lg">
          <div className="flex flex-col items-center gap-2 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg max-w-md">
            <AlertTriangle className="w-8 h-8 text-red-500" />
            <p className="text-sm font-medium text-center">{error}</p>
          </div>
        </div>
      )}

      {/* Only show control buttons if not in preview mode and showControls is true */}
      {!isPreview && showControls && (
        <div className="absolute top-4 right-4 flex flex-col gap-2 z-10">
          <Button
            size="icon"
            variant="secondary"
            onClick={handleZoomIn}
            className="rounded-full bg-white/80 dark:bg-gray-800/80 shadow-md"
          >
            <ZoomIn className="w-4 h-4" />
          </Button>
          <Button
            size="icon"
            variant="secondary"
            onClick={handleZoomOut}
            className="rounded-full bg-white/80 dark:bg-gray-800/80 shadow-md"
          >
            <ZoomOut className="w-4 h-4" />
          </Button>
          <Button
            size="icon"
            variant="secondary"
            onClick={handleReset}
            className="rounded-full bg-white/80 dark:bg-gray-800/80 shadow-md"
          >
            <Maximize className="w-4 h-4" />
          </Button>
          <Button
            size="icon"
            variant="secondary"
            onClick={() => setIsConfigOpen(true)}
            className="rounded-full bg-white/80 dark:bg-gray-800/80 shadow-md"
          >
            <Settings2 className="w-4 h-4" />
          </Button>
          <Button
            size="icon"
            variant="secondary"
            onClick={handleDownload}
            className="rounded-full bg-white/80 dark:bg-gray-800/80 shadow-md"
          >
            <Download className="w-4 h-4" />
          </Button>
        </div>
      )}

      <div
        ref={canvasContainerRef}
        className="relative flex-grow w-full h-full overflow-hidden"
      ></div>

      {/* Only include dialog for full interactive mode */}
      {!isPreview && (
        <Dialog open={isConfigOpen} onOpenChange={setIsConfigOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Display Settings</DialogTitle>
              <DialogDescription>Customize how this pattern is displayed</DialogDescription>
            </DialogHeader>
            <div className="space-y-6 py-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label htmlFor="line-width" className="text-sm font-medium">
                    Line Width: {(localLineWidth ?? globalLineWidth).toFixed(1)}
                  </label>
                </div>
                <Slider
                  id="line-width"
                  min={0.1}
                  max={5}
                  step={0.1}
                  value={[localLineWidth ?? globalLineWidth]}
                  onValueChange={(values) => {
                    setLocalLineWidth(values[0])
                  }}
                />
              </div>

              <div className="flex items-center justify-between">
                <label htmlFor="use-3d-effect" className="text-sm font-medium">
                  3D Effect
                </label>
                <Switch
                  id="use-3d-effect"
                  checked={localUse3dEffect ?? globalUse3dEffect}
                  onCheckedChange={(checked) => setLocalUse3dEffect(checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <label htmlFor="show-jump-stitches" className="text-sm font-medium">
                  Show Jump Stitches
                </label>
                <Switch
                  id="show-jump-stitches"
                  checked={showJumpStitches}
                  onCheckedChange={(checked) => setShowJumpStitches(checked)}
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label htmlFor="resolution-scale" className="text-sm font-medium">
                    Download Resolution Scale:{' '}
                    {(localResolutionScale ?? globalResolutionScale).toFixed(1)}x
                  </label>
                </div>
                <Slider
                  id="resolution-scale"
                  min={0.5}
                  max={3}
                  step={0.1}
                  value={[localResolutionScale ?? globalResolutionScale]}
                  onValueChange={(values) => {
                    setLocalResolutionScale(values[0])
                  }}
                />
              </div>

              <div className="flex justify-end">
                <Button
                  variant="outline"
                  onClick={() => {
                    setLocalLineWidth(null)
                    setLocalUse3dEffect(null)
                    setLocalResolutionScale(null)
                    setShowJumpStitches(false)
                  }}
                >
                  Reset to Global
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}
