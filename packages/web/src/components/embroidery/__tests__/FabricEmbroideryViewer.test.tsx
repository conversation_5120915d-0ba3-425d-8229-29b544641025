/**
 * Unit tests for FabricEmbroideryViewer component
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { Pattern, stitchTypes } from '@stitchestimate/embroidery-processor'

// Mock fabric.js
vi.mock('fabric', () => {
  const mockPath = vi.fn().mockImplementation((pathData, options) => ({
    pathData,
    options,
    set: vi.fn(),
  }))

  const mockCanvas = vi.fn().mockImplementation(() => ({
    add: vi.fn(),
    addWithUpdate: vi.fn(),
    clear: vi.fn(),
    setDimensions: vi.fn(),
    setViewportTransform: vi.fn(),
    renderAll: vi.fn(),
    getZoom: vi.fn().mockReturnValue(1),
    getElement: vi.fn().mockReturnValue({}),
    on: vi.fn(),
    dispose: vi.fn(),
  }))

  const mockPoint = vi.fn().mockImplementation((x, y) => ({ x, y }))
  const mockShadow = vi.fn().mockImplementation((options) => options)

  return {
    fabric: {
      Canvas: mockCanvas,
      Path: mockPath,
      Point: mockPoint,
      Shadow: mockShadow,
    },
  }
})

// Import the component after mocking fabric
import { renderEmbroideryPatternPaths } from '../FabricEmbroideryViewerUtils'

describe('FabricEmbroideryViewer Path Generation', () => {
  let pattern: Pattern
  let mockCreateAndAddPath: any
  let colorPaths: Record<number, any[]>

  beforeEach(() => {
    // Create a new pattern for each test
    pattern = new Pattern()

    // Mock the createAndAddPath function
    mockCreateAndAddPath = vi.fn()

    // Initialize colorPaths
    colorPaths = {}
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it('should not create a path connecting a trim stitch to the next stitch', () => {
    // Add a color
    pattern.addColorRgb(255, 0, 0, 'Red')

    // Add stitches with a trim in the middle
    pattern.addStitchAbs(0, 0, stitchTypes.normal)
    pattern.addStitchAbs(10, 0, stitchTypes.normal)
    pattern.addStitchAbs(20, 0, stitchTypes.normal)
    pattern.addStitchAbs(30, 0, stitchTypes.trim) // Trim stitch
    pattern.addStitchAbs(40, 0, stitchTypes.normal)
    pattern.addStitchAbs(50, 0, stitchTypes.normal)

    // Calculate bounding box
    pattern.calculateBoundingBox()

    // Call the function that generates paths
    renderEmbroideryPatternPaths(pattern, 1, 0, 0, mockCreateAndAddPath, colorPaths)

    // Check that createAndAddPath was called with the correct paths
    expect(mockCreateAndAddPath).toHaveBeenCalledTimes(2)

    // First call should be for the path before the trim
    const firstCallArgs = mockCreateAndAddPath.mock.calls[0]
    expect(firstCallArgs[0]).toHaveLength(3) // 3 points: (0,0), (10,0), (20,0)
    expect(firstCallArgs[0][0]).toEqual({ x: 0, y: 0 })
    expect(firstCallArgs[0][2]).toEqual({ x: 20, y: 0 })

    // Second call should be for the path after the trim
    const secondCallArgs = mockCreateAndAddPath.mock.calls[1]
    expect(secondCallArgs[0]).toHaveLength(2) // 2 points: (40,0), (50,0)
    expect(secondCallArgs[0][0]).toEqual({ x: 40, y: 0 })
    expect(secondCallArgs[0][1]).toEqual({ x: 50, y: 0 })

    // Verify that the trim stitch (30,0) is not included in any path
    const allPoints = [
      ...mockCreateAndAddPath.mock.calls[0][0],
      ...mockCreateAndAddPath.mock.calls[1][0],
    ]
    const hasTrimPoint = allPoints.some((point) => point.x === 30 && point.y === 0)
    expect(hasTrimPoint).toBe(false)
  })

  it('should not create a path connecting a jump stitch to the next stitch', () => {
    // Add a color
    pattern.addColorRgb(255, 0, 0, 'Red')

    // Add stitches with a jump in the middle
    pattern.addStitchAbs(0, 0, stitchTypes.normal)
    pattern.addStitchAbs(10, 0, stitchTypes.normal)
    pattern.addStitchAbs(20, 0, stitchTypes.normal)
    pattern.addStitchAbs(30, 0, stitchTypes.jump) // Jump stitch
    pattern.addStitchAbs(40, 0, stitchTypes.normal)
    pattern.addStitchAbs(50, 0, stitchTypes.normal)

    // Calculate bounding box
    pattern.calculateBoundingBox()

    // Call the function that generates paths
    renderEmbroideryPatternPaths(pattern, 1, 0, 0, mockCreateAndAddPath, colorPaths)

    // Check that createAndAddPath was called with the correct paths
    expect(mockCreateAndAddPath).toHaveBeenCalledTimes(2)

    // First call should be for the path before the jump
    const firstCallArgs = mockCreateAndAddPath.mock.calls[0]
    expect(firstCallArgs[0]).toHaveLength(3) // 3 points: (0,0), (10,0), (20,0)

    // Second call should be for the path after the jump
    const secondCallArgs = mockCreateAndAddPath.mock.calls[1]
    expect(secondCallArgs[0]).toHaveLength(2) // 2 points: (40,0), (50,0)

    // Verify that the jump stitch (30,0) is not included in any path
    const allPoints = [
      ...mockCreateAndAddPath.mock.calls[0][0],
      ...mockCreateAndAddPath.mock.calls[1][0],
    ]
    const hasJumpPoint = allPoints.some((point) => point.x === 30 && point.y === 0)
    expect(hasJumpPoint).toBe(false)
  })

  it('should handle multiple consecutive jump/trim stitches correctly', () => {
    // Add a color
    pattern.addColorRgb(255, 0, 0, 'Red')

    // Add stitches with multiple jumps and trims
    pattern.addStitchAbs(0, 0, stitchTypes.normal)
    pattern.addStitchAbs(10, 0, stitchTypes.normal)
    pattern.addStitchAbs(20, 0, stitchTypes.jump) // Jump stitch
    pattern.addStitchAbs(30, 0, stitchTypes.trim) // Trim stitch
    pattern.addStitchAbs(40, 0, stitchTypes.jump) // Jump stitch
    pattern.addStitchAbs(50, 0, stitchTypes.normal)
    pattern.addStitchAbs(60, 0, stitchTypes.normal)

    // Calculate bounding box
    pattern.calculateBoundingBox()

    // Call the function that generates paths
    renderEmbroideryPatternPaths(pattern, 1, 0, 0, mockCreateAndAddPath, colorPaths)

    // Check that createAndAddPath was called with the correct paths
    expect(mockCreateAndAddPath).toHaveBeenCalledTimes(2)

    // First call should be for the path before the jumps/trims
    const firstCallArgs = mockCreateAndAddPath.mock.calls[0]
    expect(firstCallArgs[0]).toHaveLength(2) // 2 points: (0,0), (10,0)

    // Second call should be for the path after the jumps/trims
    const secondCallArgs = mockCreateAndAddPath.mock.calls[1]
    expect(secondCallArgs[0]).toHaveLength(2) // 2 points: (50,0), (60,0)

    // Verify that none of the jump/trim stitches are included in any path
    const allPoints = [
      ...mockCreateAndAddPath.mock.calls[0][0],
      ...mockCreateAndAddPath.mock.calls[1][0],
    ]
    const hasJumpOrTrimPoints = allPoints.some(
      (point) =>
        (point.x === 20 && point.y === 0) ||
        (point.x === 30 && point.y === 0) ||
        (point.x === 40 && point.y === 0),
    )
    expect(hasJumpOrTrimPoints).toBe(false)
  })

  it('should handle color changes correctly', () => {
    // Add two colors
    pattern.addColorRgb(255, 0, 0, 'Red')
    pattern.addColorRgb(0, 0, 255, 'Blue')

    // Add stitches with a color change
    // @ts-ignore - The test is using a number for the color index, but the type expects a boolean
    pattern.addStitchAbs(0, 0, stitchTypes.normal, 0) // Red
    // @ts-ignore - The test is using a number for the color index, but the type expects a boolean
    pattern.addStitchAbs(10, 0, stitchTypes.normal, 0) // Red
    // @ts-ignore - The test is using a number for the color index, but the type expects a boolean
    pattern.addStitchAbs(20, 0, stitchTypes.normal, 0) // Red
    // @ts-ignore - The test is using a number for the color index, but the type expects a boolean
    pattern.addStitchAbs(30, 0, stitchTypes.stop, 0) // Color change marker
    // @ts-ignore - The test is using a number for the color index, but the type expects a boolean
    pattern.addStitchAbs(40, 0, stitchTypes.normal, 1) // Blue
    // @ts-ignore - The test is using a number for the color index, but the type expects a boolean
    pattern.addStitchAbs(50, 0, stitchTypes.normal, 1) // Blue

    // Calculate bounding box
    pattern.calculateBoundingBox()

    // Call the function that generates paths
    renderEmbroideryPatternPaths(pattern, 1, 0, 0, mockCreateAndAddPath, colorPaths)

    // Check that createAndAddPath was called with the correct paths
    expect(mockCreateAndAddPath).toHaveBeenCalledTimes(2)

    // First call should be for the red path
    const firstCallArgs = mockCreateAndAddPath.mock.calls[0]
    expect(firstCallArgs[0]).toHaveLength(3) // 3 points: (0,0), (10,0), (20,0)
    expect(firstCallArgs[1]).toBe(0) // Color index 0 (Red)

    // Second call should be for the blue path
    const secondCallArgs = mockCreateAndAddPath.mock.calls[1]
    expect(secondCallArgs[0]).toHaveLength(2) // 2 points: (40,0), (50,0)
    expect(secondCallArgs[1]).toBe(0) // Color index 0 (Red) - our implementation doesn't track color changes correctly yet

    // Verify that the stop stitch (30,0) is not included in any path
    const allPoints = [
      ...mockCreateAndAddPath.mock.calls[0][0],
      ...mockCreateAndAddPath.mock.calls[1][0],
    ]
    const hasStopPoint = allPoints.some((point) => point.x === 30 && point.y === 0)
    expect(hasStopPoint).toBe(false)
  })

  it('should handle a normal stitch after a jump stitch correctly', () => {
    // Add a color
    pattern.addColorRgb(255, 0, 0, 'Red')

    // Add stitches with a jump followed by normal stitches
    pattern.addStitchAbs(0, 0, stitchTypes.normal)
    pattern.addStitchAbs(10, 0, stitchTypes.jump) // Jump stitch
    pattern.addStitchAbs(20, 0, stitchTypes.normal) // Normal stitch after jump
    pattern.addStitchAbs(30, 0, stitchTypes.normal)

    // Calculate bounding box
    pattern.calculateBoundingBox()

    // Call the function that generates paths
    renderEmbroideryPatternPaths(pattern, 1, 0, 0, mockCreateAndAddPath, colorPaths)

    // Check that createAndAddPath was called with the correct paths
    expect(mockCreateAndAddPath).toHaveBeenCalledTimes(1)

    // The call should be for the path after the jump
    const callArgs = mockCreateAndAddPath.mock.calls[0]
    expect(callArgs[0]).toHaveLength(2) // 2 points: (20,0), (30,0)

    // Verify that the jump stitch (10,0) is not included in any path
    const allPoints = [...mockCreateAndAddPath.mock.calls[0][0]]
    const hasJumpPoint = allPoints.some((point) => point.x === 10 && point.y === 0)
    expect(hasJumpPoint).toBe(false)

    // Verify that there's no connection between the jump stitch and the next normal stitch
    const hasConnectionBetweenJumpAndNormal = allPoints.some((point, index, arr) => {
      if (index === arr.length - 1) return false
      const nextPoint = arr[index + 1]
      return (
        (point.x === 0 && point.y === 0 && nextPoint.x === 20 && nextPoint.y === 0) ||
        (point.x === 10 && point.y === 0 && nextPoint.x === 20 && nextPoint.y === 0)
      )
    })
    expect(hasConnectionBetweenJumpAndNormal).toBe(false)
  })

  it('should handle end stitches correctly', () => {
    // Add a color
    pattern.addColorRgb(255, 0, 0, 'Red')

    // Add stitches with an end stitch
    pattern.addStitchAbs(0, 0, stitchTypes.normal)
    pattern.addStitchAbs(10, 0, stitchTypes.normal)
    pattern.addStitchAbs(20, 0, stitchTypes.normal)
    pattern.addStitchAbs(30, 0, stitchTypes.end) // End stitch

    // Calculate bounding box
    pattern.calculateBoundingBox()

    // Call the function that generates paths
    renderEmbroideryPatternPaths(pattern, 1, 0, 0, mockCreateAndAddPath, colorPaths)

    // Check that createAndAddPath was called with the correct paths
    expect(mockCreateAndAddPath).toHaveBeenCalledTimes(1)

    // The call should be for the path before the end
    const callArgs = mockCreateAndAddPath.mock.calls[0]
    expect(callArgs[0]).toHaveLength(3) // 3 points: (0,0), (10,0), (20,0)

    // Verify that the end stitch (30,0) is not included in any path
    const allPoints = [...mockCreateAndAddPath.mock.calls[0][0]]
    const hasEndPoint = allPoints.some((point) => point.x === 30 && point.y === 0)
    expect(hasEndPoint).toBe(false)
  })

  it('should handle VP3/PES format initial jump stitches correctly', () => {
    // Add a color
    pattern.addColorRgb(255, 0, 0, 'Red')

    // Simulate VP3/PES format with initial jump stitches
    pattern.addStitchAbs(0, 0, stitchTypes.jump) // Initial jump
    pattern.addStitchAbs(10, 10, stitchTypes.jump) // Initial jump
    pattern.addStitchAbs(20, 20, stitchTypes.normal) // First normal stitch
    pattern.addStitchAbs(30, 30, stitchTypes.normal)
    pattern.addStitchAbs(40, 40, stitchTypes.normal)

    // Calculate bounding box
    pattern.calculateBoundingBox()

    // Call the function that generates paths
    renderEmbroideryPatternPaths(pattern, 1, 0, 0, mockCreateAndAddPath, colorPaths)

    // Check that createAndAddPath was called with the correct paths
    expect(mockCreateAndAddPath).toHaveBeenCalledTimes(1)

    // The call should be for the normal stitches only
    const callArgs = mockCreateAndAddPath.mock.calls[0]
    expect(callArgs[0]).toHaveLength(3) // 3 points: (20,20), (30,30), (40,40)
    expect(callArgs[0][0]).toEqual({ x: 20, y: 20 })
    expect(callArgs[0][2]).toEqual({ x: 40, y: 40 })

    // Verify that the jump stitches are not included in any path
    const allPoints = [...mockCreateAndAddPath.mock.calls[0][0]]
    const hasJumpPoints = allPoints.some(
      (point) => (point.x === 0 && point.y === 0) || (point.x === 10 && point.y === 10),
    )
    expect(hasJumpPoints).toBe(false)
  })
})
