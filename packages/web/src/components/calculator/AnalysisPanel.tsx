import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Toolbar } from './Toolbar'
import { RotateCcw, AlertTriangle, CreditCard, Info, FileText, Loader2 } from 'lucide-react'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { toast } from 'sonner'

interface AnalysisProps {
  analysis: {
    stitchCount: number
    estimatedTime?: string
    estimatedStitches?: number
    underlayStitches?: number
    totalStitches?: number
    designWidth?: number
    designHeight?: number
    isMockData?: {
      type: 'simulated' | 'fallback'
      reason?: string
    }
    [key: string]: any // Allow additional properties
  } | null
  loading: boolean
  onAnalyze: () => void
  isDirty?: boolean
  userPermissions?: {
    hasUnlimitedPlan?: boolean
    hasCredits?: boolean
    isDemo?: boolean
    isSuperAdmin?: boolean
  }
}

export function AnalysisPanel({
  analysis,
  loading,
  onAnalyze,
  isDirty,
  userPermissions = {},
}: AnalysisProps) {
  const {
    hasUnlimitedPlan = false,
    hasCredits = false,
    isDemo = false,
    isSuperAdmin = false,
  } = userPermissions

  // Debug log permissions
  console.log('AnalysisPanel permissions:', { hasUnlimitedPlan, hasCredits, isDemo, isSuperAdmin })

  const showCreditWarning = !hasUnlimitedPlan && !analysis && !isDemo && !isSuperAdmin

  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)

  const handleGeneratePDF = async () => {
    if (!analysis) return

    setIsGeneratingPDF(true)
    try {
      // Get the authentication token from cookies
      const token = document.cookie
        .split('; ')
        .find((row) => row.startsWith('payload-token='))
        ?.split('=')[1]

      const response = await fetch('/api/reports/pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token ? `JWT ${token}` : '',
        },
        body: JSON.stringify({
          estimateId: 'current',
          projectName: `Embroidery Estimate ${new Date().toLocaleDateString()}`,
          pricePerThousandStitches: 1.0, // Default price, could be made configurable
          estimate: analysis, // Pass the estimate data directly
        }),
      })

      if (!response.ok) {
        const error = await response.json().catch(() => null)
        throw new Error(error?.error || 'Failed to generate PDF')
      }

      // Get the PDF blob
      const blob = await response.blob()

      // Create a URL for the blob
      const url = window.URL.createObjectURL(blob)

      // Create a link element and trigger download
      const link = document.createElement('a')
      link.href = url
      link.download = `estimate-${Date.now()}.pdf`
      document.body.appendChild(link)
      link.click()
      link.remove()

      // Clean up the URL
      window.URL.revokeObjectURL(url)

      toast.success('PDF report generated successfully', {
        description: 'You can find all your reports in your account dashboard.',
      })
    } catch (error) {
      console.error('Error generating PDF:', error)
      toast.error('Failed to generate PDF', {
        description: 'Please try again or contact support if the problem persists.',
      })
    } finally {
      setIsGeneratingPDF(false)
    }
  }

  return (
    <Toolbar className="absolute right-4 top-4 w-[300px]">
      <div className="w-full space-y-4" data-testid="calculator-result">
        {showCreditWarning && (
          <Alert className="bg-muted border border-amber-200">
            <div className="flex items-start gap-2">
              <CreditCard className="h-4 w-4 text-amber-500 mt-0.5" />
              <AlertDescription className="text-xs">
                Generating an estimate will use 1 credit from your account.
                {!hasCredits && (
                  <span className="block mt-1 text-red-500 font-medium">
                    You have no credits available.
                  </span>
                )}
              </AlertDescription>
            </div>
          </Alert>
        )}

        {analysis ? (
          <>
            <div
              className="p-4 bg-muted rounded-lg border relative"
              data-testid="stitch-count-result"
            >
              {analysis.isMockData && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="absolute -top-2 -right-2">
                        <AlertTriangle className="h-5 w-5 text-yellow-500" />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="font-medium">Mock Data</p>
                      <p className="text-sm text-muted-foreground">
                        {analysis.isMockData.type === 'fallback'
                          ? 'Using fallback data due to API error'
                          : 'Using simulated data for development'}
                      </p>
                      {analysis.isMockData.reason && (
                        <p className="text-xs text-muted-foreground mt-1">
                          {analysis.isMockData.reason}
                        </p>
                      )}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
              <h3 className="font-semibold">Stitch Count</h3>
              <p className="text-2xl">{analysis.stitchCount.toLocaleString()}</p>
            </div>
            <div className="p-4 bg-muted rounded-lg border" data-testid="estimated-time-result">
              <h3 className="font-semibold">Estimated Time</h3>
              <p className="text-2xl">{analysis.estimatedTime || 'N/A'}</p>
            </div>
            <div className="space-y-2">
              <Button
                onClick={handleGeneratePDF}
                variant="outline"
                className="w-full flex items-center gap-2"
                data-testid="generate-pdf-button"
                disabled={isGeneratingPDF}
              >
                {isGeneratingPDF ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Generating PDF...
                  </>
                ) : (
                  <>
                    <FileText className="h-4 w-4" />
                    Generate PDF Report
                  </>
                )}
              </Button>
              {isDirty && (
                <>
                  {!hasUnlimitedPlan && !isSuperAdmin && (
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Info className="h-3 w-3" />
                      <span>Regenerating will not use additional credits</span>
                    </div>
                  )}
                  <Button
                    onClick={onAnalyze}
                    disabled={loading}
                    className="w-full flex items-center gap-2"
                    data-testid="regenerate-estimate-button"
                  >
                    <RotateCcw className="h-4 w-4" />
                    {loading ? 'Analyzing...' : 'Regenerate Estimate'}
                  </Button>
                </>
              )}
            </div>
          </>
        ) : (
          <Button
            onClick={onAnalyze}
            disabled={loading || (!hasUnlimitedPlan && !hasCredits && !isSuperAdmin && !isDemo)}
            className="w-full"
            data-testid="generate-estimate-button"
          >
            {loading ? 'Analyzing...' : 'Generate Estimate'}
          </Button>
        )}
      </div>
    </Toolbar>
  )
}
