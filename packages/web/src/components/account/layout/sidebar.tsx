'use client'

import React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/utilities'
import { buttonVariants } from '@/components/ui/button'
import { CreditCard, FileText, Home } from 'lucide-react'

const navigation = [
  {
    name: 'Overview',
    href: '/account',
    icon: Home,
  },
  {
    name: 'Credits',
    href: '/account/credits',
    icon: CreditCard,
  },
  {
    name: 'Reports',
    href: '/account/reports',
    icon: FileText,
  },
]

export function Sidebar() {
  const pathname = usePathname()

  return (
    <nav className="flex flex-col gap-2">
      {navigation.map((item) => {
        const isActive = pathname === item.href
        return (
          <Link
            key={item.name}
            href={item.href}
            className={cn(
              buttonVariants({ variant: isActive ? 'secondary' : 'ghost' }),
              'justify-start gap-2',
            )}
          >
            <item.icon className="h-4 w-4" />
            {item.name}
          </Link>
        )
      })}
    </nav>
  )
}

// Also export as default for compatibility
export default Sidebar
