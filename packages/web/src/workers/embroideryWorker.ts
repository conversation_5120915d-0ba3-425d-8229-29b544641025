import { Pattern, Color, Stitch, IPattern } from '@stitchestimate/embroidery-processor'

type RenderMessage = {
  type: 'render'
  pattern: {
    colors: Color[]
    stitches: Stitch[]
    right: number
    bottom: number
    left: number
    top: number
  }
  settings: {
    width: number
    height: number
    lineWidth: number
    use3DEffect: boolean
    shadowBlur: number
    resolutionScale: number
  }
}

// Create an offscreen canvas for rendering
let offscreenCanvas: OffscreenCanvas | null = null

self.onmessage = async (e: MessageEvent<RenderMessage>) => {
  if (e.data.type === 'render') {
    const { pattern: patternData, settings } = e.data

    // Create pattern instance from the data
    const pattern = new Pattern()
    pattern.colors = patternData.colors
    pattern.stitches = patternData.stitches
    pattern.right = patternData.right
    pattern.bottom = patternData.bottom
    pattern.left = patternData.left
    pattern.top = patternData.top

    // Create or reuse offscreen canvas
    if (!offscreenCanvas) {
      offscreenCanvas = new OffscreenCanvas(settings.width, settings.height)
    } else {
      offscreenCanvas.width = settings.width
      offscreenCanvas.height = settings.height
    }

    // Draw the pattern
    pattern.drawShape(
      offscreenCanvas as unknown as HTMLCanvasElement,
      settings.lineWidth,
      settings.use3DEffect,
      settings.shadowBlur,
      settings.resolutionScale,
    )

    // Convert to ImageBitmap for transfer
    const imageBitmap = await createImageBitmap(offscreenCanvas)

    // Post the rendered image back to the main thread
    self.postMessage({ type: 'render-complete', imageBitmap }, { transfer: [imageBitmap] })
  }
}

// TypeScript worker type declaration
export type {} // Required for TypeScript modules
