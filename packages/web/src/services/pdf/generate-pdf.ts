import { PDFDocument, rgb, StandardFonts, PDFPage } from 'pdf-lib'
import { EstimateResponse } from '@/types/api'
import { GeneratePDFOptions, PDFReportSection } from '@/types/pdf-report'

/**
 * Generates a PDF report for an embroidery estimate
 *
 * @param options Options for generating the PDF
 * @returns PDF as a Uint8Array
 */
export async function generatePDF(options: GeneratePDFOptions): Promise<Uint8Array> {
  const {
    template,
    estimate,
    companyName,
    customerName,
    projectName,
    notes,
    pricePerThousandStitches = 1.0,
  } = options

  // Create a new PDF document
  const pdfDoc = await PDFDocument.create()
  const page = pdfDoc.addPage(template.layout === 'portrait' ? [612, 792] : [792, 612])

  // Load fonts
  const regularFont = await pdfDoc.embedFont(StandardFonts.Helvetica)
  const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold)

  // Parse colors
  const primaryColor = parseColor(template.branding.primaryColor)
  const secondaryColor = parseColor(template.branding.secondaryColor)

  // Draw each section
  for (const section of template.sections) {
    await drawSection(pdfDoc, page, section, {
      estimate,
      companyName,
      customerName,
      projectName,
      notes,
      pricePerThousandStitches,
      regularFont,
      boldFont,
      primaryColor,
      secondaryColor,
    })
  }

  // Save the PDF
  return await pdfDoc.save()
}

/**
 * Draws a section on the PDF page
 */
async function drawSection(
  pdfDoc: PDFDocument,
  page: PDFPage,
  section: PDFReportSection,
  context: {
    estimate: EstimateResponse
    companyName: string
    customerName?: string
    projectName?: string
    notes?: string
    pricePerThousandStitches: number
    regularFont: any
    boldFont: any
    primaryColor: { r: number; g: number; b: number }
    secondaryColor: { r: number; g: number; b: number }
  },
) {
  const { position, style = {} } = section
  const { height: pageHeight } = page.getSize()

  // Calculate y position (PDF coordinates start from bottom)
  const y = pageHeight - position.y - position.height

  // Draw background if specified
  if (style.backgroundColor) {
    const bgColor = parseColor(style.backgroundColor)
    page.drawRectangle({
      x: position.x,
      y,
      width: position.width,
      height: position.height,
      color: rgb(bgColor.r, bgColor.g, bgColor.b),
      borderWidth: 0,
    })
  }

  // Draw content based on section type
  switch (section.type) {
    case 'header':
      drawHeader(page, section, context, y)
      break
    case 'design-preview':
      // In a real implementation, this would embed the design image
      drawDesignPreview(page, section, context, y)
      break
    case 'stitch-details':
      drawStitchDetails(page, section, context, y)
      break
    case 'pricing':
      drawPricing(page, section, context, y)
      break
    case 'notes':
      drawNotes(page, section, context, y)
      break
    case 'footer':
      drawFooter(page, section, context, y)
      break
  }
}

/**
 * Draws the header section
 */
function drawHeader(page: PDFPage, section: PDFReportSection, context: any, y: number) {
  const { position } = section
  const { companyName, boldFont, primaryColor, projectName } = context

  // Draw company name
  page.drawText(companyName, {
    x: position.x,
    y: y + position.height - 30,
    size: 24,
    font: boldFont,
    color: rgb(primaryColor.r, primaryColor.g, primaryColor.b),
  })

  // Draw report title
  page.drawText('Embroidery Stitch Estimate', {
    x: position.x,
    y: y + position.height - 60,
    size: 18,
    font: boldFont,
    color: rgb(0, 0, 0),
  })

  // Draw project name if provided
  if (projectName) {
    page.drawText(`Project: ${projectName}`, {
      x: position.x,
      y: y + position.height - 85,
      size: 12,
      font: boldFont,
      color: rgb(0, 0, 0),
    })
  }

  // Draw date
  const date = new Date().toLocaleDateString()
  page.drawText(`Date: ${date}`, {
    x: position.x,
    y: y + position.height - 105,
    size: 10,
    font: boldFont,
    color: rgb(0, 0, 0),
  })
}

/**
 * Draws the design preview section
 */
function drawDesignPreview(page: PDFPage, section: PDFReportSection, context: any, y: number) {
  const { position } = section
  const { regularFont, boldFont } = context

  // Draw section title
  page.drawText('Design Preview', {
    x: position.x,
    y: y + position.height - 20,
    size: 14,
    font: boldFont,
    color: rgb(0, 0, 0),
  })

  // In a real implementation, this would embed the design image
  // For now, just draw a placeholder
  page.drawRectangle({
    x: position.x + 10,
    y: y + 10,
    width: position.width - 20,
    height: position.height - 40,
    borderColor: rgb(0.8, 0.8, 0.8),
    borderWidth: 1,
    color: rgb(0.95, 0.95, 0.95),
  })

  page.drawText('Design Image', {
    x: position.x + position.width / 2 - 40,
    y: y + position.height / 2,
    size: 12,
    font: regularFont,
    color: rgb(0.5, 0.5, 0.5),
  })
}

/**
 * Draws the stitch details section
 */
function drawStitchDetails(page: PDFPage, section: PDFReportSection, context: any, y: number) {
  const { position } = section
  const { estimate, regularFont, boldFont, primaryColor } = context

  // Draw section title
  page.drawText('Stitch Details', {
    x: position.x,
    y: y + position.height - 20,
    size: 14,
    font: boldFont,
    color: rgb(primaryColor.r, primaryColor.g, primaryColor.b),
  })

  // Draw stitch count
  page.drawText('Total Stitch Count:', {
    x: position.x + 10,
    y: y + position.height - 50,
    size: 12,
    font: boldFont,
    color: rgb(0, 0, 0),
  })

  page.drawText(estimate.stitchCount.toLocaleString(), {
    x: position.x + 150,
    y: y + position.height - 50,
    size: 12,
    font: regularFont,
    color: rgb(0, 0, 0),
  })

  // Draw estimated time
  page.drawText('Estimated Time:', {
    x: position.x + 10,
    y: y + position.height - 70,
    size: 12,
    font: boldFont,
    color: rgb(0, 0, 0),
  })

  page.drawText(estimate.estimatedTime, {
    x: position.x + 150,
    y: y + position.height - 70,
    size: 12,
    font: regularFont,
    color: rgb(0, 0, 0),
  })

  // Draw dimensions
  page.drawText('Dimensions:', {
    x: position.x + 10,
    y: y + position.height - 90,
    size: 12,
    font: boldFont,
    color: rgb(0, 0, 0),
  })

  page.drawText(`${estimate.designWidth} × ${estimate.designHeight} mm`, {
    x: position.x + 150,
    y: y + position.height - 90,
    size: 12,
    font: regularFont,
    color: rgb(0, 0, 0),
  })

  // Draw garment type
  page.drawText('Garment Type:', {
    x: position.x + 10,
    y: y + position.height - 110,
    size: 12,
    font: boldFont,
    color: rgb(0, 0, 0),
  })

  page.drawText(estimate.garmentType || 'Not specified', {
    x: position.x + 150,
    y: y + position.height - 110,
    size: 12,
    font: regularFont,
    color: rgb(0, 0, 0),
  })
}

/**
 * Draws the pricing section
 */
function drawPricing(page: PDFPage, section: PDFReportSection, context: any, y: number) {
  const { position } = section
  const { estimate, pricePerThousandStitches, regularFont, boldFont, primaryColor } = context

  // Draw section title
  page.drawText('Pricing Estimate', {
    x: position.x,
    y: y + position.height - 20,
    size: 14,
    font: boldFont,
    color: rgb(primaryColor.r, primaryColor.g, primaryColor.b),
  })

  // Draw price per thousand stitches
  page.drawText('Price per 1,000 stitches:', {
    x: position.x + 10,
    y: y + position.height - 50,
    size: 12,
    font: boldFont,
    color: rgb(0, 0, 0),
  })

  page.drawText(`$${pricePerThousandStitches.toFixed(2)}`, {
    x: position.x + 170,
    y: y + position.height - 50,
    size: 12,
    font: regularFont,
    color: rgb(0, 0, 0),
  })

  // Draw total price
  const totalPrice = (estimate.stitchCount / 1000) * pricePerThousandStitches

  page.drawText('Estimated Total:', {
    x: position.x + 10,
    y: y + position.height - 70,
    size: 12,
    font: boldFont,
    color: rgb(0, 0, 0),
  })

  page.drawText(`$${totalPrice.toFixed(2)}`, {
    x: position.x + 170,
    y: y + position.height - 70,
    size: 12,
    font: regularFont,
    color: rgb(0, 0, 0),
  })

  // Note about pricing
  page.drawText('Note: Final pricing may vary based on complexity,', {
    x: position.x + 10,
    y: y + position.height - 100,
    size: 9,
    font: regularFont,
    color: rgb(0.4, 0.4, 0.4),
  })

  page.drawText('thread changes, and other factors.', {
    x: position.x + 10,
    y: y + position.height - 115,
    size: 9,
    font: regularFont,
    color: rgb(0.4, 0.4, 0.4),
  })
}

/**
 * Draws the notes section
 */
function drawNotes(page: PDFPage, section: PDFReportSection, context: any, y: number) {
  const { position } = section
  const { notes, regularFont, boldFont, primaryColor } = context

  // Draw section title
  page.drawText('Notes', {
    x: position.x,
    y: y + position.height - 20,
    size: 14,
    font: boldFont,
    color: rgb(primaryColor.r, primaryColor.g, primaryColor.b),
  })

  // Draw notes if provided
  if (notes) {
    // Simple text wrapping (in a real implementation, this would be more sophisticated)
    const words = notes.split(' ')
    let line = ''
    let lineY = y + position.height - 50
    const maxWidth = position.width - 20
    const lineHeight = 15

    for (const word of words) {
      const testLine = line + (line ? ' ' : '') + word
      const textWidth = regularFont.widthOfTextAtSize(testLine, 10)

      if (textWidth > maxWidth) {
        page.drawText(line, {
          x: position.x + 10,
          y: lineY,
          size: 10,
          font: regularFont,
          color: rgb(0, 0, 0),
        })
        line = word
        lineY -= lineHeight
      } else {
        line = testLine
      }
    }

    // Draw the last line
    if (line) {
      page.drawText(line, {
        x: position.x + 10,
        y: lineY,
        size: 10,
        font: regularFont,
        color: rgb(0, 0, 0),
      })
    }
  } else {
    page.drawText('No additional notes.', {
      x: position.x + 10,
      y: y + position.height - 50,
      size: 10,
      font: regularFont,
      color: rgb(0.4, 0.4, 0.4),
    })
  }
}

/**
 * Draws the footer section
 */
function drawFooter(page: PDFPage, section: PDFReportSection, context: any, y: number) {
  const { companyName, regularFont } = context
  const { width } = page.getSize()

  // Draw company name
  page.drawText(`${companyName} - Generated by StitchEstimate`, {
    x: width / 2 - 100,
    y: y + 15,
    size: 10,
    font: regularFont,
    color: rgb(0.4, 0.4, 0.4),
  })

  // Draw page number
  page.drawText('Page 1 of 1', {
    x: width - 70,
    y: y + 15,
    size: 10,
    font: regularFont,
    color: rgb(0.4, 0.4, 0.4),
  })
}

/**
 * Parses a color string (hex) into RGB values
 */
function parseColor(color: string): { r: number; g: number; b: number } {
  // Default to black if color is invalid
  if (!color || !color.startsWith('#')) {
    return { r: 0, g: 0, b: 0 }
  }

  // Remove # and parse hex values
  const hex = color.substring(1)
  const r = parseInt(hex.substring(0, 2), 16) / 255
  const g = parseInt(hex.substring(2, 4), 16) / 255
  const b = parseInt(hex.substring(4, 6), 16) / 255

  return { r, g, b }
}
