import { useEffect, useRef, useCallback } from 'react'
import { IPattern } from '@/types/embroidery-shared'

type RenderSettings = {
  width: number
  height: number
  lineWidth: number
  use3DEffect: boolean
  shadowBlur: number
  resolutionScale: number
}

export function useEmbroideryWorker() {
  const workerRef = useRef<Worker | null>(null)

  useEffect(() => {
    // Initialize worker
    workerRef.current = new Worker(new URL('../workers/embroideryWorker.ts', import.meta.url), {
      type: 'module',
    })

    // Cleanup on unmount
    return () => {
      workerRef.current?.terminate()
      workerRef.current = null
    }
  }, [])

  const renderPattern = useCallback(
    (pattern: IPattern, settings: RenderSettings): Promise<ImageBitmap> => {
      return new Promise((resolve, reject) => {
        if (!workerRef.current) {
          reject(new Error('Worker not initialized'))
          return
        }

        const handleMessage = (e: MessageEvent) => {
          if (e.data.type === 'render-complete') {
            workerRef.current?.removeEventListener('message', handleMessage)
            resolve(e.data.imageBitmap)
          }
        }

        workerRef.current.addEventListener('message', handleMessage)

        // Send pattern data to worker
        workerRef.current.postMessage(
          {
            type: 'render',
            pattern: {
              colors: pattern.colors,
              stitches: pattern.stitches,
              right: pattern.right,
              bottom: pattern.bottom,
              left: pattern.left,
              top: pattern.top,
            },
            settings,
          },
          // No transferables in the initial message
        )
      })
    },
    [],
  )

  return { renderPattern }
}
