'use client'

import { useState, useCallback, useEffect, useRef } from 'react'
import { UploadCloud, X, Settings2, Download, ZoomIn, Maximize, ZoomOut } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { SUPPORTED_FORMATS } from '@/types/embroidery-shared'
import { Slider } from '@/components/ui/slider'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog'
import { Switch } from '@/components/ui/switch'
import { debounce } from 'lodash'
import { Button } from '@/components/ui/button'
import FabricEmbroideryViewer from '@/components/embroidery/FabricEmbroideryViewer'
import { EmbroideryFileMetadata } from '@/types/embroidery-shared'

export default function FabricEmbroideryClient() {
  const [files, setFiles] = useState<File[]>([])
  const [dragActive, setDragActive] = useState(false)
  const [globalLineWidth, setGlobalLineWidth] = useState(1)
  const [globalUse3dEffect, setGlobalUse3dEffect] = useState(false)
  const [globalResolutionScale, setGlobalResolutionScale] = useState(1)
  const [isGlobalSettingsOpen, setIsGlobalSettingsOpen] = useState(false)

  // Add intermediate states for smooth slider movement
  const [intermediateLineWidth, setIntermediateLineWidth] = useState<number | null>(null)
  const [intermediateResolutionScale, setIntermediateResolutionScale] = useState<number | null>(
    null,
  )

  // Create debounced setters for performance
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSetGlobalLineWidth = useCallback(
    debounce((value: number) => {
      setGlobalLineWidth(value)
    }, 300),
    [
      /* dependency array intentionally left empty to avoid recreating the debounced function */
    ],
  )

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSetGlobalResolutionScale = useCallback(
    debounce((value: number) => {
      setGlobalResolutionScale(value)
    }, 300),
    [
      /* dependency array intentionally left empty to avoid recreating the debounced function */
    ],
  )

  // Use intermediate values for display, but effective values for rendering
  const displayLineWidth = intermediateLineWidth ?? globalLineWidth
  const displayResolutionScale = intermediateResolutionScale ?? globalResolutionScale

  // Add state to track metadata for each file
  const [fileMetadata, setFileMetadata] = useState<Record<number, EmbroideryFileMetadata | null>>(
    {},
  )

  // Add state for the modal
  const [isViewerModalOpen, setIsViewerModalOpen] = useState(false)
  const [currentViewFile, setCurrentViewFile] = useState<File | null>(null)

  // Add a ref for the files container to scroll to
  const uploadedFilesContainerRef = useRef<HTMLDivElement>(null)

  // Effect to hide/show the server-rendered SEO content
  useEffect(() => {
    const seoContentContainer = document.getElementById('seo-content-container')
    if (seoContentContainer) {
      seoContentContainer.style.display = files.length > 0 ? 'none' : 'block'
    }
  }, [files.length])

  // Effect to scroll to the uploaded files when they change from 0 to some files
  useEffect(() => {
    if (files.length > 0 && uploadedFilesContainerRef.current) {
      // Small delay to ensure the content is rendered properly before scrolling
      setTimeout(() => {
        uploadedFilesContainerRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        })
      }, 100)
    }
  }, [files.length]) // Run when files array length changes

  // Handler for metadata changes
  const handleMetadataChange = (index: number, metadata: EmbroideryFileMetadata | null) => {
    setFileMetadata((prev) => ({
      ...prev,
      [index]: metadata,
    }))
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const newFiles = Array.from(e.dataTransfer.files)
      setFiles((prev) => [...prev, ...newFiles])
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files)
      setFiles((prev) => [...prev, ...newFiles])
    }
  }

  const handleRemoveFile = (index: number) => {
    setFiles((prev) => prev.filter((_, i) => i !== index))
  }

  const handleRemoveAllFiles = () => {
    setFiles([])
  }

  // Create an accept string for the file input
  const acceptFormats = SUPPORTED_FORMATS.map((format) => `.${format}`).join(',')

  // Handler for opening the full viewer modal
  const handleOpenFullViewer = (file: File) => {
    setCurrentViewFile(file)
    setIsViewerModalOpen(true)
  }

  // Handler for downloading embroidery PNG
  const handleDownload = (file: File) => {
    // Download functionality is handled directly in the component
    // Create an event to trigger download on the component
    const downloadEvent = new CustomEvent('download-embroidery', {
      detail: { fileName: file.name },
    })
    document.dispatchEvent(downloadEvent)
  }

  // Additional handlers for the modal controls
  const handleZoomInModal = () => {
    // Create an event to trigger zoom in on the component
    document.dispatchEvent(
      new CustomEvent('zoom-embroidery', {
        detail: { fileName: currentViewFile?.name, action: 'zoom-in' },
      }),
    )
  }

  const handleZoomOutModal = () => {
    // Create an event to trigger zoom out on the component
    document.dispatchEvent(
      new CustomEvent('zoom-embroidery', {
        detail: { fileName: currentViewFile?.name, action: 'zoom-out' },
      }),
    )
  }

  const handleResetViewModal = () => {
    // Create an event to trigger reset view on the component
    document.dispatchEvent(
      new CustomEvent('zoom-embroidery', {
        detail: { fileName: currentViewFile?.name, action: 'reset' },
      }),
    )
  }

  return (
    <>
      <Card className="bg-white dark:bg-gray-800 shadow-md rounded-lg mb-8">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-semibold text-gray-800 dark:text-gray-200">
            {files.length === 0 ? 'Upload Embroidery Files' : 'Add More Files'}
          </CardTitle>
          <CardDescription className="text-gray-500 dark:text-gray-400">
            Drag and drop or click to upload embroidery files
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div
            className={`flex flex-col items-center justify-center p-6 rounded-lg text-center transition-colors duration-200 border-2 border-dashed border-gray-300 dark:border-gray-600 ${
              dragActive ? 'bg-primary/20 dark:bg-primary/30' : 'bg-gray-50 dark:bg-gray-700'
            }`}
            onDragEnter={handleDrag}
            onDragOver={handleDrag}
            onDragLeave={handleDrag}
            onDrop={handleDrop}
          >
            <div className="w-12 h-12 mb-3 rounded-full bg-primary/20 dark:bg-primary/30 flex items-center justify-center">
              <UploadCloud className="w-6 h-6 text-primary" />
            </div>

            <h3 className="text-base font-semibold mb-2 text-gray-800 dark:text-gray-200">
              Drag and drop your embroidery files here
            </h3>

            <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">
              Supported formats: {SUPPORTED_FORMATS.map((f) => f.toUpperCase()).join(', ')}
            </p>

            <label htmlFor="file-upload" className="cursor-pointer">
              <div className="bg-primary hover:bg-primary/80 text-white px-4 py-2 rounded-md transition-colors duration-200 text-sm">
                Select Files
              </div>
              <input
                id="file-upload"
                name="file-upload"
                type="file"
                multiple
                accept={acceptFormats}
                className="sr-only"
                onChange={handleFileChange}
                data-testid="file-input"
              />
            </label>
          </div>
        </CardContent>
      </Card>

      {files.length > 0 && (
        <div className="space-y-6" ref={uploadedFilesContainerRef}>
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200">
              {files.length} {files.length === 1 ? 'File' : 'Files'} Uploaded
            </h2>
            <div className="flex gap-4">
              <button
                onClick={() => setIsGlobalSettingsOpen(true)}
                className="text-primary hover:text-primary/80 text-sm font-medium flex items-center gap-2"
                aria-label="Global render settings"
                data-testid="global-settings-button"
              >
                <Settings2 className="w-4 h-4" />
                Global Settings
              </button>
              <button
                onClick={handleRemoveAllFiles}
                className="text-primary hover:text-primary/80 text-sm font-medium"
                aria-label="Remove all files"
              >
                Remove all files
              </button>
            </div>
          </div>

          <Dialog open={isGlobalSettingsOpen} onOpenChange={setIsGlobalSettingsOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Global Render Settings</DialogTitle>
                <DialogDescription>Adjust how all files are displayed</DialogDescription>
              </DialogHeader>
              <div className="space-y-6 py-4">
                <div className="space-y-2">
                  <label htmlFor="global-line-width" className="text-sm font-medium">
                    Line Width: {displayLineWidth.toFixed(1)}
                  </label>
                  <Slider
                    id="global-line-width"
                    min={0.1}
                    max={5}
                    step={0.1}
                    value={[displayLineWidth]}
                    onValueChange={(values) => {
                      const value = values[0]
                      setIntermediateLineWidth(value)
                      debouncedSetGlobalLineWidth(value)
                    }}
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label htmlFor="global-3d-effect" className="text-sm font-medium">
                      3D Embroidery Effect
                    </label>
                    <Switch
                      id="global-3d-effect"
                      checked={globalUse3dEffect}
                      onCheckedChange={setGlobalUse3dEffect}
                    />
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Creates a realistic 3D embroidery appearance with highlights and shadows
                  </p>
                </div>

                <div className="space-y-2">
                  <label htmlFor="global-resolution-scale" className="text-sm font-medium">
                    Resolution Scale: {displayResolutionScale.toFixed(1)}x
                  </label>
                  <Slider
                    id="global-resolution-scale"
                    min={0.5}
                    max={3}
                    step={0.1}
                    value={[displayResolutionScale]}
                    onValueChange={(values) => {
                      const value = values[0]
                      setIntermediateResolutionScale(value)
                      debouncedSetGlobalResolutionScale(value)
                    }}
                  />
                </div>
              </div>
            </DialogContent>
          </Dialog>

          <div className="columns-1 md:columns-2 lg:columns-3 gap-6">
            {files.map((file, index) => (
              <div key={`${file.name}-${index}`} className="break-inside-avoid mb-6">
                <Card className="bg-white dark:bg-gray-800 shadow-md">
                  <CardHeader className="pb-4">
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-lg font-semibold text-gray-800 dark:text-gray-200 truncate pr-4">
                        {file.name}
                      </CardTitle>
                      <button
                        onClick={() => handleRemoveFile(index)}
                        className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                        aria-label={`Remove ${file.name}`}
                        data-testid="remove-file-button"
                      >
                        <X className="w-5 h-5" />
                      </button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div
                      className="relative rounded-lg overflow-hidden bg-gray-50 dark:bg-gray-800 group flex items-center justify-center"
                      style={{
                        minHeight: '250px',
                        height: fileMetadata[index]
                          ? (() => {
                              // Calculate aspect ratio and apply appropriate scaling
                              const heightValue =
                                typeof fileMetadata[index]?.height === 'number'
                                  ? (fileMetadata[index]?.height as number)
                                  : (fileMetadata[index]?.height as { value: number })?.value || 0
                              const widthValue =
                                typeof fileMetadata[index]?.width === 'number'
                                  ? (fileMetadata[index]?.width as number)
                                  : (fileMetadata[index]?.width as { value: number })?.value || 1
                              const aspectRatio = heightValue / widthValue

                              // Determine optimal multiplier based on aspect ratio
                              let multiplier

                              if (aspectRatio > 1.8) {
                                // Very tall, narrow design
                                multiplier = 420
                              } else if (aspectRatio > 1.3) {
                                // Tall design
                                multiplier = 380
                              } else if (aspectRatio > 0.8) {
                                // Nearly square design
                                multiplier = 340
                              } else if (aspectRatio > 0.5) {
                                // Somewhat wide design
                                multiplier = 300
                              } else {
                                // Very wide, short design
                                multiplier = 260
                              }

                              // Apply calculated height with limits
                              return `${Math.min(600, Math.max(250, aspectRatio * multiplier))}px`
                            })()
                          : '400px',
                        transition: 'height 0.3s ease-in-out',
                        willChange: 'transform', // Optimize for animations
                        contain: 'strict', // Improve rendering performance
                      }}
                      data-testid="embroidery-preview-container"
                    >
                      {/* Static preview canvas with flex centering */}
                      <div
                        className="w-full h-full flex items-center justify-center"
                        style={{ willChange: 'transform' }}
                      >
                        <FabricEmbroideryViewer
                          file={file}
                          globalLineWidth={globalLineWidth}
                          globalUse3dEffect={globalUse3dEffect}
                          globalResolutionScale={globalResolutionScale}
                          onMetadataChange={(metadata) => handleMetadataChange(index, metadata)}
                          isPreview={true}
                        />
                      </div>

                      {/* Zoom overlay that appears on hover */}
                      <div
                        className="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 flex items-center justify-center transition-opacity duration-200 cursor-pointer"
                        onClick={() => handleOpenFullViewer(file)}
                        aria-label="View in full screen"
                      >
                        <div className="bg-white dark:bg-gray-800 rounded-full p-3 shadow-lg">
                          <ZoomIn className="w-6 h-6 text-primary" />
                        </div>
                      </div>
                    </div>

                    {/* Add metadata section below the canvas */}
                    <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
                      <div className="grid grid-cols-3 gap-2 text-xs">
                        <div className="px-1">
                          <span className="font-medium text-gray-500 dark:text-gray-400">
                            Dimensions
                          </span>
                          <p className="mt-1">
                            {fileMetadata[index]
                              ? (() => {
                                  const width =
                                    typeof fileMetadata[index]?.width === 'number'
                                      ? (fileMetadata[index]?.width as number).toFixed(1)
                                      : (
                                          fileMetadata[index]?.width as { value: number }
                                        )?.value.toFixed(1)
                                  const height =
                                    typeof fileMetadata[index]?.height === 'number'
                                      ? (fileMetadata[index]?.height as number).toFixed(1)
                                      : (
                                          fileMetadata[index]?.height as { value: number }
                                        )?.value.toFixed(1)
                                  const unit =
                                    typeof fileMetadata[index]?.width !== 'number'
                                      ? (fileMetadata[index]?.width as { unit: string })?.unit ||
                                        'mm'
                                      : 'mm'
                                  return `${width} × ${height} ${unit}`
                                })()
                              : '—'}
                          </p>
                        </div>
                        <div className="px-1">
                          <span className="font-medium text-gray-500 dark:text-gray-400">
                            Stitches
                          </span>
                          <p className="mt-1">
                            {fileMetadata[index]?.stitchCount?.toLocaleString() || '—'}
                          </p>
                        </div>
                        <div className="px-1">
                          <span className="font-medium text-gray-500 dark:text-gray-400">
                            Colors
                          </span>
                          <p className="mt-1">{fileMetadata[index]?.colorCount || '—'}</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>

          <div className="flex justify-center mt-8">
            <label htmlFor="add-more-files" className="cursor-pointer">
              <div className="bg-primary hover:bg-primary/80 text-white px-5 py-3 rounded-md transition-colors duration-200">
                Add More Files
              </div>
              <input
                id="add-more-files"
                name="add-more-files"
                type="file"
                multiple
                accept={acceptFormats}
                className="sr-only"
                onChange={handleFileChange}
              />
            </label>
          </div>
        </div>
      )}

      {/* Full-screen interactive viewer modal */}
      <Dialog open={isViewerModalOpen} onOpenChange={setIsViewerModalOpen} modal={true}>
        <DialogContent className="max-w-[95vw] w-[1400px] max-h-[98vh] overflow-y-auto">
          <DialogTitle className="text-gray-800 dark:text-gray-200 flex-shrink-0 py-3">
            {currentViewFile?.name || 'Embroidery Viewer'}
          </DialogTitle>

          {/* Content area that can scroll as needed */}
          <div className="flex flex-col lg:flex-row gap-4 lg:gap-6">
            {/* Main canvas area - with flex centering */}
            <div className="relative flex-1 bg-gray-50 dark:bg-gray-900/30 rounded-lg flex items-center justify-center">
              {currentViewFile && (
                <div className="w-full h-[300px] md:h-[500px] lg:h-[600px] flex items-center justify-center">
                  <FabricEmbroideryViewer
                    file={currentViewFile}
                    globalLineWidth={globalLineWidth}
                    globalUse3dEffect={globalUse3dEffect}
                    globalResolutionScale={globalResolutionScale}
                    key={`modal-viewer-${currentViewFile.name}`}
                    showControls={false}
                  />
                </div>
              )}

              {/* Control buttons on canvas */}
              <div className="absolute top-4 right-4 flex flex-col gap-2 z-10">
                <Button
                  size="icon"
                  variant="secondary"
                  onClick={handleZoomInModal}
                  className="rounded-full bg-white/80 dark:bg-gray-800/80 shadow-md"
                >
                  <ZoomIn className="w-4 h-4" />
                </Button>
                <Button
                  size="icon"
                  variant="secondary"
                  onClick={handleZoomOutModal}
                  className="rounded-full bg-white/80 dark:bg-gray-800/80 shadow-md"
                >
                  <ZoomOut className="w-4 h-4" />
                </Button>
                <Button
                  size="icon"
                  variant="secondary"
                  onClick={handleResetViewModal}
                  className="rounded-full bg-white/80 dark:bg-gray-800/80 shadow-md"
                >
                  <Maximize className="w-4 h-4" />
                </Button>
                <Button
                  size="icon"
                  variant="secondary"
                  onClick={() => currentViewFile && handleDownload(currentViewFile)}
                  className="rounded-full bg-white/80 dark:bg-gray-800/80 shadow-md"
                >
                  <Download className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Settings sidebar */}
            <div className="lg:w-80 md:w-72 w-full border-t lg:border-t-0 lg:border-l border-gray-200 dark:border-gray-700 pt-4 lg:pl-6 lg:pt-0">
              <Card className="bg-white dark:bg-gray-800 shadow-sm">
                <CardContent className="p-4 space-y-5">
                  {/* Design Information Section */}
                  <div>
                    <h3 className="text-sm font-medium mb-3 text-gray-900 dark:text-gray-100">
                      Design Information
                    </h3>
                    {currentViewFile &&
                      fileMetadata[files.findIndex((f) => f.name === currentViewFile.name)] && (
                        <div className="grid grid-cols-2 lg:grid-cols-1 gap-3 text-xs">
                          <div className="bg-gray-50 dark:bg-gray-900/20 p-2 rounded-md">
                            <span className="font-medium text-gray-500 dark:text-gray-400 block mb-1">
                              Dimensions
                            </span>
                            <p className="truncate">
                              {(() => {
                                const index = files.findIndex(
                                  (f) => f.name === currentViewFile.name,
                                )
                                if (index !== -1 && fileMetadata[index]) {
                                  const width =
                                    typeof fileMetadata[index]?.width === 'number'
                                      ? (fileMetadata[index]?.width as number).toFixed(1)
                                      : (
                                          fileMetadata[index]?.width as { value: number }
                                        )?.value.toFixed(1)
                                  const height =
                                    typeof fileMetadata[index]?.height === 'number'
                                      ? (fileMetadata[index]?.height as number).toFixed(1)
                                      : (
                                          fileMetadata[index]?.height as { value: number }
                                        )?.value.toFixed(1)
                                  const unit =
                                    typeof fileMetadata[index]?.width !== 'number'
                                      ? (fileMetadata[index]?.width as { unit: string })?.unit ||
                                        'mm'
                                      : 'mm'
                                  return `${width} × ${height} ${unit}`
                                }
                                return '—'
                              })()}
                            </p>
                          </div>
                          <div className="bg-gray-50 dark:bg-gray-900/20 p-2 rounded-md">
                            <span className="font-medium text-gray-500 dark:text-gray-400 block mb-1">
                              Stitches
                            </span>
                            <p className="truncate">
                              {(() => {
                                const index = files.findIndex(
                                  (f) => f.name === currentViewFile.name,
                                )
                                return index !== -1
                                  ? fileMetadata[index]?.stitchCount?.toLocaleString() || '—'
                                  : '—'
                              })()}
                            </p>
                          </div>
                          <div className="bg-gray-50 dark:bg-gray-900/20 p-2 rounded-md">
                            <span className="font-medium text-gray-500 dark:text-gray-400 block mb-1">
                              Colors
                            </span>
                            <p className="truncate">
                              {(() => {
                                const index = files.findIndex(
                                  (f) => f.name === currentViewFile.name,
                                )
                                return index !== -1 ? fileMetadata[index]?.colorCount || '—' : '—'
                              })()}
                            </p>
                          </div>
                        </div>
                      )}
                  </div>

                  {/* Divider */}
                  <div className="border-t border-gray-200 dark:border-gray-700"></div>

                  {/* Display Settings Section */}
                  <div>
                    <h3 className="text-sm font-medium mb-3 text-gray-900 dark:text-gray-100">
                      Display Settings
                    </h3>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <label htmlFor="modal-line-width" className="text-xs font-medium block">
                          Line Width: {displayLineWidth.toFixed(1)}
                        </label>
                        <Slider
                          id="modal-line-width"
                          min={0.1}
                          max={5}
                          step={0.1}
                          value={[displayLineWidth]}
                          onValueChange={(values) => {
                            const value = values[0]
                            setIntermediateLineWidth(value)
                            debouncedSetGlobalLineWidth(value)
                          }}
                        />
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <label htmlFor="modal-3d-effect" className="text-xs font-medium">
                            3D Embroidery Effect
                          </label>
                          <Switch
                            id="modal-3d-effect"
                            checked={globalUse3dEffect}
                            onCheckedChange={setGlobalUse3dEffect}
                          />
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Creates a realistic 3D appearance
                        </p>
                      </div>

                      <div className="space-y-2">
                        <label
                          htmlFor="modal-resolution-scale"
                          className="text-xs font-medium block"
                        >
                          Resolution Scale: {displayResolutionScale.toFixed(1)}x
                        </label>
                        <Slider
                          id="modal-resolution-scale"
                          min={0.5}
                          max={3}
                          step={0.1}
                          value={[displayResolutionScale]}
                          onValueChange={(values) => {
                            const value = values[0]
                            setIntermediateResolutionScale(value)
                            debouncedSetGlobalResolutionScale(value)
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
