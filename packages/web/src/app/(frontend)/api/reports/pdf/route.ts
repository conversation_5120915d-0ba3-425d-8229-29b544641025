import { NextResponse } from 'next/server'
import { getPayload } from 'payload'
import configPromise from '@payload-config'
import { getAuthenticatedUser } from '@/utilities/auth/getAuthenticatedUser'
import { generatePDF } from '@/services/pdf/generate-pdf'
import { PDFReportTemplate } from '@/types/pdf-report'

export async function POST(req: Request) {
  try {
    // Get authenticated user
    const user = await getAuthenticatedUser()

    // For demo purposes, we'll allow unauthenticated users to generate PDFs
    // In a production environment, you might want to restrict this to authenticated users only
    // if (!user) {
    //   return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    // }

    // Parse request data
    const data = await req.json()
    const { estimateId, projectName, pricePerThousandStitches, estimate } = data

    if (!estimate) {
      return NextResponse.json({ error: 'Estimate data is required' }, { status: 400 })
    }

    // Create a default template
    const template: PDFReportTemplate = {
      id: 'default',
      name: 'Default Template',
      description: 'Default PDF report template',
      layout: 'portrait',
      sections: [
        {
          type: 'header',
          position: {
            x: 50,
            y: 50,
            width: 512,
            height: 120,
          },
        },
        {
          type: 'design-preview',
          position: {
            x: 50,
            y: 180,
            width: 512,
            height: 200,
          },
        },
        {
          type: 'stitch-details',
          position: {
            x: 50,
            y: 390,
            width: 250,
            height: 150,
          },
        },
        {
          type: 'pricing',
          position: {
            x: 310,
            y: 390,
            width: 250,
            height: 150,
          },
        },
        {
          type: 'notes',
          position: {
            x: 50,
            y: 550,
            width: 512,
            height: 100,
          },
        },
        {
          type: 'footer',
          position: {
            x: 50,
            y: 700,
            width: 512,
            height: 40,
          },
        },
      ],
      branding: {
        primaryColor: '#3b5bdb',
        secondaryColor: '#748ffc',
        fontFamily: 'Helvetica',
      },
    }

    // Generate PDF
    const pdfOptions = {
      template,
      estimate,
      companyName: 'StitchEstimate',
      projectName: projectName || `Embroidery Estimate ${new Date().toLocaleDateString()}`,
      pricePerThousandStitches: pricePerThousandStitches || 1.0,
    }

    const pdfBytes = await generatePDF(pdfOptions)

    // Store PDF using PayloadCMS if user is authenticated
    if (user) {
      const payload = await getPayload({ config: configPromise })
      const timestamp = Date.now()
      const filename = `${user.id}-${estimateId || 'current'}-${timestamp}.pdf`
      const projectTitle = projectName || `Embroidery Estimate ${new Date().toLocaleDateString()}`

      // Create a buffer from the Uint8Array
      const buffer = Buffer.from(pdfBytes)

      // Create the PDF report document with the file
      await payload.create({
        collection: 'pdf-reports',
        data: {
          name: projectTitle,
          user: user.id,
          estimate: estimateId || 'current',
          created: new Date().toISOString(),
          status: 'available',
        },
        file: {
          data: buffer,
          mimetype: 'application/pdf',
          name: filename,
          size: buffer.length,
        },
      })

      console.log(`[API] Uploaded PDF with filename: ${filename}`)
    }

    // Return PDF data and report info
    return new NextResponse(pdfBytes, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="estimate-${estimateId || 'current'}.pdf"`,
      },
    })
  } catch (error) {
    console.error('Error generating PDF report:', error)
    return NextResponse.json({ error: 'Failed to generate PDF report' }, { status: 500 })
  }
}
