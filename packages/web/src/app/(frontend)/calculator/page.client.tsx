'use client'
import { useState, useRef, useEffect } from 'react'
import { DimensionControls } from '@/components/calculator/DimensionControls'
import { GarmentTypeSelector } from '@/components/calculator/GarmentTypeSelector.client'
import { AnalysisPanel } from '@/components/calculator/AnalysisPanel'
import { ImageWorkspace } from '@/components/calculator/ImageWorkspace.client'
import { ImageUploaderClient } from '@/components/calculator/ImageUploader.client'
import { EstimateResponse } from '@/types/calculator'
import { PhysicalDimensions as Dimensions } from '@/types/core'
import { removeBackground, detectBackground } from '@/utilities/backgroundRemoval'
import { BackgroundRemovalDialog } from '@/components/calculator/BackgroundRemovalDialog'
import debounce from 'lodash/debounce'
import { toast } from 'sonner'
import type { User, DemoImage } from '@/payload-types'
import { useUser } from '@/hooks/useUser' // Import the useUser hook
import { debug } from '@/components/calculator/canvas/debug'

const STANDARD_DPI = 96

// Mock data for development environment
const MOCK_ESTIMATE_RESPONSE = {
  stitchCount: 12345,
  estimatedTime: '2-3 hours',
  underlayStitches: 1234,
  actualAreaMm2: 10000,
  designWidth: 100,
  designHeight: 100,
  totalPixels: 10000,
  filledPixels: 8000,
  fillRatio: 0.8,
  garmentType: 'test-garment',
  isMockData: {
    type: 'simulated' as const,
    reason: 'Development mode simulated response',
  },
}

interface CalculatorClientProps {
  initialGarmentTypes: Array<{
    id: string
    name: string
    slug: string
    svgTemplate: string
    dimensions: {
      width: number
      height: number
      units: 'in' | 'mm'
    }
  }>
  initialDemoImages: DemoImage[]
  initialUser: User | null
  initialUserPermissions: {
    isSuperAdmin: boolean
    hasUnlimitedPlan: boolean
    hasCredits: boolean
  }
}

export default function CalculatorClient({
  initialGarmentTypes,
  initialDemoImages,
  initialUser: _, // Renamed to _ to indicate intentionally unused
  initialUserPermissions, // Keep for initial render, but use hook state for logic
}: CalculatorClientProps) {
  // Use the useUser hook to get live user state and permissions
  const {
    user: currentUser,
    loading: _userLoading, // Prefixed with underscore to indicate intentionally unused
    isSuperAdmin,
    hasUnlimitedPlan,
    hasCredits,
  } = useUser()

  const [isImageUploaded, setIsImageUploaded] = useState(false)
  const [image, setImage] = useState<File | null>(null)
  const [imageUrl, setImageUrl] = useState<string | null>(null)
  const [dimensions, setDimensions] = useState<Dimensions>({ width: 0, height: 0, units: 'in' })
  const [units, setUnits] = useState<'in' | 'mm'>('in')
  const [garmentType, setGarmentType] = useState('')
  const [loading, setLoading] = useState(false)
  const [analysis, setAnalysis] = useState<EstimateResponse | null>(null)
  const [currentBackground, setCurrentBackground] = useState<
    | {
        url: string
        dimensions: {
          width: number
          height: number
          units: 'in' | 'mm'
        }
      }
    | undefined
  >(undefined)
  const aspectRatio = useRef(1)
  const prevUnits = useRef(units)
  const [showBackgroundDialog, setShowBackgroundDialog] = useState(false)
  const [processingBackground, setProcessingBackground] = useState(false)
  const [pendingFile, setPendingFile] = useState<File | null>(null)
  const [backgroundRemovalProgress, setBackgroundRemovalProgress] = useState<{
    key: string
    progress: number
  } | null>(null)
  const dimensionUpdateSource = useRef<'canvas' | 'controls' | null>(null)
  const debouncedSetDimensions = useRef(
    debounce((newDimensions: Dimensions) => {
      setDimensions(newDimensions)
    }, 16), // Roughly one frame at 60fps
  ).current
  const [isDirty, setIsDirty] = useState(false)
  const [isDemo, setIsDemo] = useState(false)
  const lastAnalysisRef = useRef<{
    dimensions: Dimensions
    units: 'in' | 'mm'
    garmentType: string
  } | null>(null)
  const [serverUnavailable, setServerUnavailable] = useState(false)
  const [errorMessage, setErrorMessage] = useState<string | null>(null)

  useEffect(() => {
    return () => {
      debouncedSetDimensions.cancel()
    }
  }, [debouncedSetDimensions])

  useEffect(() => {
    if (!garmentType) return

    const selectedGarment = initialGarmentTypes.find((g) => g.slug === garmentType)
    if (selectedGarment) {
      // Convert the garment dimensions to pixels (assuming 96 DPI)
      const widthPx = Math.round(
        selectedGarment.dimensions.width *
          (selectedGarment.dimensions.units === 'in' ? 96 : 96 / 25.4),
      )
      const heightPx = Math.round(
        selectedGarment.dimensions.height *
          (selectedGarment.dimensions.units === 'in' ? 96 : 96 / 25.4),
      )

      // Add explicit width and height to the SVG if it doesn't have them
      let svgContent = selectedGarment.svgTemplate
        .replace('width="100%"', `width="${widthPx}"`)
        .replace('height="100%"', `height="${heightPx}"`)

      // If no width/height attributes exist, add them
      if (!svgContent.includes('width=')) {
        svgContent = svgContent.replace('<svg', `<svg width="${widthPx}" height="${heightPx}"`)
      }

      // Ensure the SVG has explicit dimensions and encode it properly for use as an image source
      const encodedSvg = encodeURIComponent(svgContent).replace(/'/g, '%27').replace(/"/g, '%22')

      debug.log('Setting background with SVG:', {
        originalDimensions: selectedGarment.dimensions,
        pixelDimensions: { width: widthPx, height: heightPx },
        svgContent,
      })

      setCurrentBackground({
        url: `data:image/svg+xml;charset=UTF-8,${encodedSvg}`,
        dimensions: selectedGarment.dimensions,
      })
    }
  }, [garmentType, initialGarmentTypes])

  const handleImageUpload = async (event: { target: { files: FileList | null } }) => {
    if (!event.target.files || event.target.files.length === 0) return
    const file = event.target.files[0]
    if (!file) return

    // Check if this is a demo image based on naming convention
    const isDemoFile = file.name === 'demo-image.png' || file.name.startsWith('demo-')
    if (isDemoFile) {
      console.log('Demo image detected during upload:', file.name)
      setIsDemo(true)
    }

    // Reset the server unavailability state for each new image upload
    // This allows the system to try server-side processing again
    setServerUnavailable(false)

    // Store the file for potential background removal
    setPendingFile(file)

    // Store the image file for later reference (important for demo detection)
    setImage(file)

    // Show the image immediately with a specific upload ID
    // This unique ID helps prevent duplicate images by tracking this specific upload operation
    const uploadId = `upload-${Date.now()}`
    processImage(file, uploadId)

    try {
      // Use the lightweight detection method
      const hasBackground = await detectBackground(file)

      if (hasBackground) {
        setShowBackgroundDialog(true)
      } else {
        // Image is already shown and no background to remove
        setPendingFile(null)
      }
    } catch (error) {
      console.error('Error detecting background:', error)
      // Image is already shown, just clean up pending state
      setPendingFile(null)
    }
  }

  /**
   * Handles the user choice to keep the background of the image.
   *
   * IMPORTANT: Even though we're not removing the background, we still need to
   * generate a new processing ID and reprocess the image to ensure it's properly
   * loaded in the Canvas component.
   *
   * This fixes a race condition where:
   * 1. The image is initially loaded with an "upload-ID" when first selected
   * 2. If the user chooses "Keep Background", we previously didn't generate a new ID
   * 3. Without a new ID, the Canvas component might not recognize it as a new loading request
   * 4. This resulted in no image showing up when "Keep Background" was selected
   *
   * The solution is to always process with a unique ID regardless of the user's choice,
   * which ensures the Canvas component treats it as a new image operation.
   */
  const handleKeepBackground = () => {
    // No need to process image again since it's already shown
    // Just let the Component know that we're keeping the existing image
    // This avoids duplicate processing which could create duplicate images
    setPendingFile(null)
    setShowBackgroundDialog(false)

    // Add a unique ID for the "keep background" operation to ensure proper tracking
    // Even though we're using the same file, we need a new processing ID to trigger
    // the proper image loading in Canvas component
    if (pendingFile) {
      const keepId = `keep-${Date.now()}`
      if (process.env.NODE_ENV !== 'production') {
        console.log(`[Image Processing] Reprocessing with keepId: ${keepId}`)
      }

      // Process the image again with the new ID
      processImage(pendingFile, keepId)
    }
  }

  const handleRemoveBackground = async () => {
    if (!pendingFile) return

    setProcessingBackground(true)
    setBackgroundRemovalProgress(null)
    setServerUnavailable(false)
    setErrorMessage(null)

    try {
      console.log('[Background Removal] Starting background removal process with utility')

      // Use the removeBackground utility function directly which handles both remote and fallback
      const processedBlob = await removeBackground(pendingFile, {
        onProgress: (progress) => {
          setBackgroundRemovalProgress(progress)
        },
        onFallbackToClient: () => {
          console.log('[Background Removal] Falling back to client-side processing')
          setServerUnavailable(true)
        },
      })

      console.log('[Background Removal] Process completed, creating file from processed blob')
      const processedFile = new File([processedBlob], pendingFile.name, {
        type: 'image/png',
      })

      // Process the image with the background removed with a specific removal ID
      // This unique ID helps track this specific removal operation to prevent duplicates
      const removalId = `removal-${Date.now()}`
      processImage(processedFile, removalId)

      // Clean up state after successful processing
      setProcessingBackground(false)
      setPendingFile(null)
      setShowBackgroundDialog(false)
    } catch (error) {
      // Handle any remaining errors
      console.error('[Background Removal] Error:', error)
      setErrorMessage('An error occurred during background removal. Please try again.')
      setProcessingBackground(false)
    }
  }

  /**
   * Processes an image file and prepares it for display on the canvas.
   *
   * DUPLICATE PREVENTION STRATEGY:
   * 1. Each image processing operation gets a unique operationId (upload-ID or removal-ID)
   *    which is appended to the image URL as a fragment (#) to track specific operations
   * 2. We clean up existing URL and image state before processing a new image
   * 3. We use setTimeout when updating the image URL to ensure proper React batching
   *    and prevent race conditions that could lead to duplicate images
   * 4. There's a matching loading lock mechanism in the Canvas component that prevents
   *    concurrent image loading operations
   *
   * RACE CONDITION FIX:
   * The key insight that fixed the "Keep Background" bug was ensuring that every user action
   * (upload, keep background, remove background) generates a unique processing ID. This ensures
   * the Canvas component treats each operation as distinct even when using the same image file.
   *
   * This function works together with:
   * 1. The enhanced loading mechanism in Canvas.tsx that respects processing IDs
   * 2. The fixed metadata assignment in image-manager.ts that properly stores these IDs
   * 3. The timeout delay in the Canvas component that allows state to settle
   *
   * @param file - The image file to process
   * @param operationId - Optional unique ID to track this specific image processing operation
   */
  const processImage = (file: File, operationId?: string) => {
    // Generate a unique ID for this image processing action
    const processingId = operationId || Date.now().toString()

    if (process.env.NODE_ENV !== 'production') {
      console.log(`[Image Processing] Processing image with ID: ${processingId}`)
    }

    // Clean up any existing image state
    setIsImageUploaded(false)
    if (imageUrl) {
      // Make sure to handle URLs with fragments correctly
      URL.revokeObjectURL(imageUrl.split('#')[0])
      setImageUrl(null)
    }
    setImage(null)

    // Create and process the new image
    const url = URL.createObjectURL(file)
    const img = new Image()

    img.onload = () => {
      aspectRatio.current = img.width / img.height
      const initialWidth = Math.min(img.width / STANDARD_DPI, 10)
      setDimensions({
        width: Number(initialWidth.toFixed(2)),
        height: Number((initialWidth / aspectRatio.current).toFixed(2)),
        units,
      })
      setImage(file)

      // Store the processing ID as a fragment in the URL
      // Use setTimeout to ensure React batches this update separately to avoid race conditions
      setTimeout(() => {
        if (process.env.NODE_ENV !== 'production') {
          console.log(`[Image Processing] Setting image URL with ID: ${processingId}`)
        }
        setImageUrl(`${url}#${processingId}`)
        setIsImageUploaded(true)
      }, 0)
    }

    img.onerror = () => {
      URL.revokeObjectURL(url)
      toast.error('Failed to load image', {
        description: 'The selected file could not be loaded as an image.',
      })
    }

    img.src = url
  }

  useEffect(() => {
    if (prevUnits.current !== units && dimensions.width && dimensions.height) {
      prevUnits.current = units
    }
  }, [units, dimensions])

  const handleDimensionChange = (dimension: 'width' | 'height', value: number) => {
    value = Number(value)
    if (isNaN(value) || value <= 0) return

    dimensionUpdateSource.current = 'controls'
    const newDimensions = { ...dimensions }
    if (dimension === 'width') {
      newDimensions.width = value
      newDimensions.height = Number((value / aspectRatio.current).toFixed(2))
    } else {
      newDimensions.height = value
      newDimensions.width = Number((value * aspectRatio.current).toFixed(2))
    }
    debouncedSetDimensions(newDimensions)
  }

  const handleCanvasDimensionsChange = (newDimensions: Dimensions) => {
    if (dimensionUpdateSource.current === 'controls') {
      dimensionUpdateSource.current = null
      return
    }

    dimensionUpdateSource.current = 'canvas'

    // Ensure dimensions are properly rounded
    const roundedDimensions: Dimensions = {
      width: Number(newDimensions.width.toFixed(2)),
      height: Number(newDimensions.height.toFixed(2)),
      units: newDimensions.units,
    }

    setDimensions(roundedDimensions)
    aspectRatio.current = roundedDimensions.width / roundedDimensions.height
    dimensionUpdateSource.current = null
  }

  const handleSubmit = async () => {
    if (!image) return

    setLoading(true)
    try {
      // Convert dimensions to mm for the API
      const apiDimensions =
        units === 'mm'
          ? { width: dimensions.width, height: dimensions.height }
          : {
              width: dimensions.width * 25.4,
              height: dimensions.height * 25.4,
            }

      // Create form data
      const formData = new FormData()
      formData.append('image', image)
      formData.append(
        'data',
        JSON.stringify({
          dimensions: apiDimensions,
          units: 'mm', // Always send as mm to the API
          garmentType,
          is_demo: isDemo, // Add flag indicating if this is a demo estimate (no credit deduction)
        }),
      )

      // Submit form
      const response = await fetch('/api/estimate', {
        method: 'POST',
        body: formData,
      })

      // Log the request status for debugging
      console.log(`Estimate request completed with status: ${response.status}, isDemo: ${isDemo}`)

      if (!response.ok) {
        const errorData = await response.json().catch(() => null)
        throw new Error(
          errorData?.message || `Failed to generate estimate (Status: ${response.status})`,
        )
      }

      const data = await response.json()
      setAnalysis(data)
      setIsDirty(false)
      lastAnalysisRef.current = { dimensions, units, garmentType }
    } catch (error) {
      console.error('Error generating estimate:', error)

      // Show user-friendly error message
      toast.error('Failed to generate estimate', {
        description:
          process.env.NODE_ENV === 'development'
            ? error.message
            : 'Please try again later or contact support if the problem persists.',
      })

      // In development, fallback to mock data after error
      if (process.env.NODE_ENV === 'development') {
        await new Promise((resolve) => setTimeout(resolve, 1000))
        toast.info('Falling back to mock data', {
          description: 'The API request failed. Using simulated data for development purposes.',
          duration: 5000,
        })
        setAnalysis({
          ...MOCK_ESTIMATE_RESPONSE,
          isMockData: {
            type: 'fallback',
            reason: `API Error: ${error.message}`,
          },
        })
        setIsDirty(false)
        lastAnalysisRef.current = { dimensions, units, garmentType }
      }
    } finally {
      setLoading(false)
    }
  }

  const handleUnitsChange = (newUnits: 'in' | 'mm') => {
    let newWidth: number = 0
    let newHeight: number = 0

    if (newUnits === 'mm' && units === 'in') {
      newWidth = dimensions.width * 25.4
      newHeight = dimensions.height * 25.4
    } else if (newUnits === 'in' && units === 'mm') {
      newWidth = dimensions.width / 25.4
      newHeight = dimensions.height / 25.4
    } else {
      newWidth = dimensions.width
      newHeight = dimensions.height
    }

    dimensionUpdateSource.current = 'controls'
    setDimensions({
      width: Number(newWidth.toFixed(2)),
      height: Number(newHeight.toFixed(2)),
      units: newUnits,
    })
    dimensionUpdateSource.current = null
    setUnits(newUnits)
  }

  // Commented out as it's not currently used
  // const selectedGarment = initialGarmentTypes.find((type) => type.slug === garmentType)

  useEffect(() => {
    if (!analysis) return

    const currentParams = {
      dimensions,
      units,
      garmentType,
    }

    if (!lastAnalysisRef.current) {
      lastAnalysisRef.current = currentParams
      return
    }

    const last = lastAnalysisRef.current
    const isDifferent =
      last.dimensions.width !== dimensions.width ||
      last.dimensions.height !== dimensions.height ||
      last.units !== units ||
      last.garmentType !== garmentType

    setIsDirty(isDifferent)
  }, [dimensions, units, garmentType, analysis])

  // Effect to detect demo images
  useEffect(() => {
    // Check if this is a demo image based on name patterns
    const isDemoImage = image?.name === 'demo-image.png' || !!image?.name?.startsWith('demo-')

    setIsDemo(isDemoImage)

    // Log demo status for debugging
    if (isDemoImage) {
      console.log('Demo image detected:', image?.name)
    }
  }, [image])

  // Listen for custom demo image detected events (from tests or other components)
  useEffect(() => {
    const handleDemoDetected = (event: Event) => {
      const customEvent = event as CustomEvent
      console.log('Demo image event detected', customEvent.detail)
      setIsDemo(true)
    }

    window.addEventListener('demo-image-detected', handleDemoDetected)

    return () => {
      window.removeEventListener('demo-image-detected', handleDemoDetected)
    }
  }, [])

  const handleReset = () => {
    // Clean up existing image state
    if (imageUrl) {
      URL.revokeObjectURL(imageUrl)
    }
    setImageUrl(null)
    setImage(null)
    setIsImageUploaded(false)
    setDimensions({ width: 0, height: 0, units })
    setAnalysis(null)
    setIsDirty(false)
    lastAnalysisRef.current = null
    aspectRatio.current = 1
  }

  return (
    <div className="flex-1 flex flex-col">
      <div className="container-wide p-4 flex-1">
        <div className="relative w-full min-h-[calc(100vh-8rem)] bg-muted rounded-lg overflow-hidden">
          {!isImageUploaded ? (
            <ImageUploaderClient
              demoImages={initialDemoImages}
              // Pass the current user state from the hook
              user={currentUser}
              // Pass the permissions from the hook
              isSuperAdmin={isSuperAdmin}
              hasUnlimitedPlan={hasUnlimitedPlan}
              hasCredits={hasCredits}
              onUpload={handleImageUpload}
            />
          ) : (
            <>
              <ImageWorkspace
                imageUrl={imageUrl || ''}
                dimensions={dimensions}
                units={units}
                onDimensionsChange={handleCanvasDimensionsChange}
                onReset={handleReset}
                backgroundImage={currentBackground}
              />
              <DimensionControls
                units={units}
                dimensions={dimensions}
                onUnitsChange={handleUnitsChange}
                onDimensionChange={handleDimensionChange}
              />
              <GarmentTypeSelector
                value={garmentType}
                onChange={setGarmentType}
                options={initialGarmentTypes}
              />
              <AnalysisPanel
                analysis={analysis}
                loading={loading}
                onAnalyze={handleSubmit}
                isDirty={isDirty}
                userPermissions={{
                  hasUnlimitedPlan: initialUserPermissions.hasUnlimitedPlan,
                  hasCredits: initialUserPermissions.hasCredits,
                  isDemo: isDemo,
                  isSuperAdmin: initialUserPermissions.isSuperAdmin, // Pass the isSuperAdmin flag
                }}
              />
            </>
          )}
        </div>
      </div>

      <BackgroundRemovalDialog
        open={showBackgroundDialog}
        processing={processingBackground}
        progress={backgroundRemovalProgress}
        onOpenChange={setShowBackgroundDialog}
        onKeepBackground={handleKeepBackground}
        onRemoveBackground={handleRemoveBackground}
        serverUnavailable={serverUnavailable}
        errorMessage={errorMessage}
      />
    </div>
  )
}
