/**
 * Core type definitions used across the application
 */

/**
 * Measurement units supported by the application
 */
export type Units = 'in' | 'mm'

/**
 * Physical dimensions with measurement units
 */
export interface PhysicalDimensions {
  /** Width in specified units */
  width: number
  /** Height in specified units */
  height: number
  /** Measurement units */
  units: Units
}

/**
 * Constants for unit conversion
 */
export interface UnitConversionConstants {
  /** Standard screen DPI */
  DPI: number
  /** Millimeters per inch */
  MM_PER_INCH: number
}

/**
 * Extended fabric.js Image type with additional properties
 */
export interface FabricImage extends fabric.Image {
  /** Image width */
  width?: number
  /** Image height */
  height?: number
  /** Horizontal scale factor */
  scaleX?: number
  /** Vertical scale factor */
  scaleY?: number
  /** Left position */
  left?: number
  /** Top position */
  top?: number
  /** Whether this is a background image */
  isBackground?: boolean
  /** Render the image to a canvas context */
  render(ctx: CanvasRenderingContext2D): void
}

/**
 * Extended fabric.js Canvas type with units
 */
export interface FabricCanvasWithUnits extends fabric.Canvas {
  /** Units of measurement */
  units?: Units
}

/**
 * Global window augmentation for Fabric.js canvas instance
 */
declare global {
  interface Window {
    /** Global canvas instance for testing */
    __FABRIC_CANVAS__: FabricCanvasWithUnits
  }
}

/**
 * Generic canvas context interface that works with both browser and node-canvas
 */
export interface GenericCanvasContext {
  save(): void
  restore(): void
  beginPath(): void
  closePath(): void
  moveTo(x: number, y: number): void
  lineTo(x: number, y: number): void
  stroke(): void
  fill(): void
  fillRect(x: number, y: number, width: number, height: number): void
  strokeRect(x: number, y: number, width: number, height: number): void
  clearRect(x: number, y: number, width: number, height: number): void
  arc(
    x: number,
    y: number,
    radius: number,
    startAngle: number,
    endAngle: number,
    counterclockwise?: boolean,
  ): void
  ellipse(
    x: number,
    y: number,
    radiusX: number,
    radiusY: number,
    rotation: number,
    startAngle: number,
    endAngle: number,
    counterclockwise?: boolean,
  ): void

  // Style properties
  lineWidth: number
  strokeStyle: string
  fillStyle: string
  shadowBlur: number
  shadowColor: string
  shadowOffsetX: number
  shadowOffsetY: number
  lineCap: CanvasLineCap
  lineJoin: CanvasLineJoin
  globalAlpha: number

  // Text methods
  fillText(text: string, x: number, y: number, maxWidth?: number): void
  strokeText(text: string, x: number, y: number, maxWidth?: number): void
  measureText(text: string): { width: number }

  // Transformation methods
  scale(x: number, y: number): void
  rotate(angle: number): void
  translate(x: number, y: number): void
  transform(a: number, b: number, c: number, d: number, e: number, f: number): void
  setTransform(a: number, b: number, c: number, d: number, e: number, f: number): void
  resetTransform(): void
}
