/**
 * Shared types for embroidery processing
 *
 * This file temporarily contains local type definitions while we fix
 * the embroidery-processor package exports. These should eventually
 * be imported from @stitchestimate/embroidery-processor.
 */

/**
 * Stitch types enum
 */
export const stitchTypes = {
  normal: 0,
  jump: 1,
  trim: 2,
  stop: 4,
  end: 8,
}

/**
 * Stitch class for representing a single stitch in an embroidery pattern
 */
export class Stitch {
  constructor(
    public x: number,
    public y: number,
    public flags: number,
    public color: number,
  ) {}
}

/**
 * Color class for representing a thread color in an embroidery pattern
 */
export class Color {
  constructor(
    public r: number,
    public g: number,
    public b: number,
    public description: string = '',
  ) {}
}

/**
 * Pattern interface for representing an embroidery design
 */
export interface IPattern {
  colors: Color[]
  stitches: Stitch[]
  hoop: Record<string, any>
  lastX: number
  lastY: number
  top: number
  bottom: number
  left: number
  right: number
  currentColorIndex: number

  addColor(color: Color): void
  addColorRgb(r: number, g: number, b: number, description?: string): void
  addStitchAbs(x: number, y: number, flags: number, isAutoColorIndex?: boolean): void
  addStitchRel(dx: number, dy: number, flags: number, isAutoColorIndex?: boolean): void
  calculateBoundingBox(): void
  moveToPositive(): void
  invertPatternVertical(): void
  fixColorCount(): void
}

/**
 * Pattern class placeholder - will be imported from embroidery-processor
 */
export class Pattern implements IPattern {
  colors: Color[] = []
  stitches: Stitch[] = []
  hoop: Record<string, any> = {}
  lastX: number = 0
  lastY: number = 0
  top: number = 0
  bottom: number = 0
  left: number = 0
  right: number = 0
  currentColorIndex: number = 0

  addColor(color: Color): void {
    this.colors.push(color)
  }

  addColorRgb(r: number, g: number, b: number, description?: string): void {
    this.addColor(new Color(r, g, b, description || ''))
  }

  addStitchAbs(x: number, y: number, flags: number, isAutoColorIndex?: boolean): void {
    this.stitches.push(new Stitch(x, y, flags, this.currentColorIndex))
    this.lastX = x
    this.lastY = y
  }

  addStitchRel(dx: number, dy: number, flags: number, isAutoColorIndex?: boolean): void {
    this.addStitchAbs(this.lastX + dx, this.lastY + dy, flags, isAutoColorIndex)
  }

  calculateBoundingBox(): void {
    if (this.stitches.length === 0) return

    this.left = Math.min(...this.stitches.map((s) => s.x))
    this.right = Math.max(...this.stitches.map((s) => s.x))
    this.top = Math.min(...this.stitches.map((s) => s.y))
    this.bottom = Math.max(...this.stitches.map((s) => s.y))
  }

  moveToPositive(): void {
    const offsetX = -this.left
    const offsetY = -this.top

    this.stitches.forEach((stitch) => {
      stitch.x += offsetX
      stitch.y += offsetY
    })

    this.calculateBoundingBox()
  }

  invertPatternVertical(): void {
    const centerY = (this.top + this.bottom) / 2
    this.stitches.forEach((stitch) => {
      stitch.y = centerY * 2 - stitch.y
    })
    this.calculateBoundingBox()
  }

  fixColorCount(): void {
    // Implementation would go here
  }
}

// Temporary supported formats - should come from embroidery-processor
export const SUPPORTED_FORMATS = ['dst', 'exp', 'jef', 'pes', 'vp3']

// Temporary processing function - should come from embroidery-processor
export async function processEmbroideryBuffer(
  arrayBuffer: ArrayBuffer,
  filename: string,
): Promise<Pattern> {
  // This is a placeholder - the real implementation is in embroidery-processor
  throw new Error('processEmbroideryBuffer not implemented - should use embroidery-processor')
}

/**
 * Metadata about an embroidery file
 */
export interface EmbroideryFileMetadata {
  /** Format of the embroidery file */
  format: string
  /** Width of the design in pixels or units */
  width: number | { value: number; unit: string }
  /** Height of the design in pixels or units */
  height: number | { value: number; unit: string }
  /** Number of stitches in the design */
  stitchCount: number
  /** Number of colors in the design */
  colorCount: number
  /** Original filename */
  filename: string
}

/**
 * Function type for reading embroidery files
 */
export type FormatReader = (buffer: ArrayBuffer, pattern: IPattern) => void

/**
 * File type set for embroidery formats
 */
export interface FileTypeSet {
  /** Display name of the format */
  name: string
  /** File extension without dot */
  extension: string
  /** MIME type */
  mimeType: string
  /** Reader function */
  read: FormatReader
}

/**
 * Return type for the processEmbroideryFile function
 */
export interface EmbroideryProcessingResult {
  /** Metadata about the embroidery file */
  metadata: EmbroideryFileMetadata
  /** The processed pattern object */
  pattern: IPattern
}
