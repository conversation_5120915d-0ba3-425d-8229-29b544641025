/**
 * Shared types for embroidery processing
 *
 * This file re-exports types from @stitchestimate/embroidery-processor
 * to maintain a single source of truth for type definitions.
 */

// Import types from the embroidery-processor package
import { Pattern, IPattern, Color, Stitch, stitchTypes } from '@stitchestimate/embroidery-processor'

// Re-export the core types to maintain compatibility
export { Pattern, Color, Stitch, stitchTypes }
export type { IPattern }

/**
 * Metadata about an embroidery file
 */
export interface EmbroideryFileMetadata {
  /** Format of the embroidery file */
  format: string
  /** Width of the design in pixels or units */
  width: number | { value: number; unit: string }
  /** Height of the design in pixels or units */
  height: number | { value: number; unit: string }
  /** Number of stitches in the design */
  stitchCount: number
  /** Number of colors in the design */
  colorCount: number
  /** Original filename */
  filename: string
}

/**
 * Function type for reading embroidery files
 */
export type FormatReader = (buffer: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, pattern: IPattern) => void

/**
 * File type set for embroidery formats
 */
export interface FileTypeSet {
  /** Display name of the format */
  name: string
  /** File extension without dot */
  extension: string
  /** MIME type */
  mimeType: string
  /** Reader function */
  read: FormatReader
}

/**
 * Return type for the processEmbroideryFile function
 */
export interface EmbroideryProcessingResult {
  /** Metadata about the embroidery file */
  metadata: EmbroideryFileMetadata
  /** The processed pattern object */
  pattern: IPattern
}
