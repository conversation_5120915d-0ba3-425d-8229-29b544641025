/**
 * Types for stitch file processing
 *
 * @deprecated Use types from @stitchestimate/shared instead
 */

import type { IPattern } from './embroidery-shared'

/**
 * Function type for reading embroidery files
 * @deprecated Use FormatReader from @stitchestimate/shared instead
 */
export type FormatReader = (buffer: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, pattern: IPattern) => void

/**
 * File type set for embroidery formats
 * @deprecated Use FileTypeSet from @stitchestimate/shared instead
 */
export interface FileTypeSet {
  /** Display name of the format */
  name: string
  /** File extension without dot */
  extension: string
  /** MIME type */
  mimeType: string
  /** Reader function */
  read: FormatReader
}
