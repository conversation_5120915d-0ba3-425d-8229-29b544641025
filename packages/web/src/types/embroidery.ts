import { EmbroideryFileMetadata, stitchTypes, Color, Stitch, IPattern } from './embroidery-shared'

// Re-export the types from our shared types
export type { EmbroideryFileMetadata, Color, Stitch, IPattern }

// Export the stitch types
export { stitchTypes as StitchType }

// Legacy types (deprecated) - kept for backwards compatibility
/**
 * @deprecated Use Color from types/embroidery-shared instead
 */
export interface LegacyEmbroideryColor {
  r: number
  g: number
  b: number
  description: string
}

/**
 * @deprecated Use Stitch from types/embroidery-shared instead
 */
export interface LegacyEmbroideryStitch {
  flags: number
  x: number
  y: number
  color: number
}

/**
 * @deprecated Use IPattern from types/embroidery-shared instead
 */
export interface EmbroideryPattern {
  colors: LegacyEmbroideryColor[]
  stitches: LegacyEmbroideryStitch[]
  hoop: Record<string, any>
  top: number
  bottom: number
  left: number
  right: number
}

/**
 * @deprecated Use EmbroideryFileMetadata from types/embroidery-shared instead
 */
export interface EmbroideryMetadata {
  fileName: string
  fileFormat: string
  stitchCount: number
  colorCount: number
  dimensions: {
    width: number
    height: number
  }
}

/**
 * @deprecated Use stitchTypes enum from types/embroidery-shared instead
 */
export const StitchTypes = {
  normal: 0,
  jump: 1,
  trim: 2,
  stop: 4,
  end: 8,
}
