import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { logger } from './logger'

describe('logger', () => {
  // Spy on console methods
  beforeEach(() => {
    vi.spyOn(console, 'debug').mockImplementation(() => {})
    vi.spyOn(console, 'log').mockImplementation(() => {})
    vi.spyOn(console, 'warn').mockImplementation(() => {})
    vi.spyOn(console, 'error').mockImplementation(() => {})
  })

  afterEach(() => {
    vi.restoreAllMocks()
    // Reset logger configuration
    logger.configure({
      enabled: true,
      minLevel: 'INFO',
      showOffsets: false,
      modulePrefix: 'StitchEstimate',
    })
  })

  it('should log messages with the correct prefix', () => {
    logger.log('Test message')
    expect(console.log).toHaveBeenCalledWith('[StitchEstimate] Test message')
  })

  it('should not log debug messages when minLevel is INFO', () => {
    logger.debug('Debug message')
    expect(console.debug).not.toHaveBeenCalled()
  })

  it('should log debug messages when minLevel is DEBUG', () => {
    logger.configure({ minLevel: 'DEBUG' })
    logger.debug('Debug message')
    expect(console.debug).toHaveBeenCalledWith('[StitchEstimate] Debug message')
  })

  it('should not log any messages when disabled', () => {
    logger.configure({ enabled: false })
    logger.log('Test message')
    logger.warn('Warning message')
    logger.error('Error message')
    expect(console.log).not.toHaveBeenCalled()
    expect(console.warn).not.toHaveBeenCalled()
    expect(console.error).not.toHaveBeenCalled()
  })

  it('should create a format logger with the correct prefix', () => {
    const formatLogger = logger.createFormatLogger('TEST')
    formatLogger.log('Format message')
    expect(console.log).toHaveBeenCalledWith('[StitchEstimate:TEST] Format message')
  })

  it('should show offsets in format logger when enabled', () => {
    logger.configure({ showOffsets: true })
    const formatLogger = logger.createFormatLogger('TEST')
    formatLogger.log('Format message', 255)
    expect(console.log).toHaveBeenCalledWith('[StitchEstimate:TEST] [0x00ff] Format message')
  })

  it('should handle error objects correctly', () => {
    const error = new Error('Test error')
    logger.error('Error occurred', error)
    expect(console.error).toHaveBeenCalledWith('[StitchEstimate] Error occurred', error)
  })
})
