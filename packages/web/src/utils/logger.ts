/**
 * Standardized logging utility
 * Provides consistent log formatting and the ability to enable/disable logs by category
 */

// Configure log levels
const LOG_LEVELS = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
} as const

type LogLevel = keyof typeof LOG_LEVELS

// Check if we're in a production environment
const isProduction = (): boolean => {
  try {
    // Check for Node.js environment
    if (typeof process !== 'undefined' && process.env && process.env.NODE_ENV) {
      return process.env.NODE_ENV === 'production'
    }
    // In browser, assume development unless explicitly set
    return false
  } catch (e) {
    // If any errors occur, assume development
    return false
  }
}

// Default configuration
const config = {
  enabled: !isProduction(),
  minLevel: LOG_LEVELS.INFO as number,
  modulePrefix: 'StitchEstimate',
  showOffsets: false, // Whether to show binary offsets in logs
}

export const logger = {
  /**
   * Configure logger settings
   */
  configure: (options: {
    enabled?: boolean
    minLevel?: LogLevel
    showOffsets?: boolean
    modulePrefix?: string
  }) => {
    if (options.enabled !== undefined) config.enabled = options.enabled
    if (options.minLevel !== undefined) config.minLevel = LOG_LEVELS[options.minLevel]
    if (options.showOffsets !== undefined) config.showOffsets = options.showOffsets
    if (options.modulePrefix !== undefined) config.modulePrefix = options.modulePrefix
  },

  /**
   * Log a debug message
   */
  debug: (message: string, ...args: any[]) => {
    if (!config.enabled || LOG_LEVELS.DEBUG < config.minLevel) return
    console.debug(`[${config.modulePrefix}] ${message}`, ...args)
  },

  /**
   * Log an informational message
   */
  log: (message: string, ...args: any[]) => {
    if (!config.enabled || LOG_LEVELS.INFO < config.minLevel) return
    console.log(`[${config.modulePrefix}] ${message}`, ...args)
  },

  /**
   * Log a warning message
   */
  warn: (message: string, ...args: any[]) => {
    if (!config.enabled || LOG_LEVELS.WARN < config.minLevel) return
    console.warn(`[${config.modulePrefix}] ${message}`, ...args)
  },

  /**
   * Log an error message
   */
  error: (message: string, error?: unknown) => {
    if (!config.enabled || LOG_LEVELS.ERROR < config.minLevel) return
    if (error) {
      console.error(`[${config.modulePrefix}] ${message}`, error)
    } else {
      console.error(`[${config.modulePrefix}] ${message}`)
    }
  },

  /**
   * Create a logger for a specific format
   */
  createFormatLogger: (formatName: string) => {
    const prefix = `${config.modulePrefix}:${formatName}`
    return {
      debug: (message: string, offset?: number, ...args: any[]) => {
        if (!config.enabled || LOG_LEVELS.DEBUG < config.minLevel) return
        const offsetStr =
          offset !== undefined && config.showOffsets
            ? `[0x${offset.toString(16).padStart(4, '0')}] `
            : ''
        console.debug(`[${prefix}] ${offsetStr}${message}`, ...args)
      },
      log: (message: string, offset?: number, ...args: any[]) => {
        if (!config.enabled || LOG_LEVELS.INFO < config.minLevel) return
        const offsetStr =
          offset !== undefined && config.showOffsets
            ? `[0x${offset.toString(16).padStart(4, '0')}] `
            : ''
        console.log(`[${prefix}] ${offsetStr}${message}`, ...args)
      },
      warn: (message: string, offset?: number, ...args: any[]) => {
        if (!config.enabled || LOG_LEVELS.WARN < config.minLevel) return
        const offsetStr =
          offset !== undefined && config.showOffsets
            ? `[0x${offset.toString(16).padStart(4, '0')}] `
            : ''
        console.warn(`[${prefix}] ${offsetStr}${message}`, ...args)
      },
      error: (message: string, error?: unknown, offset?: number) => {
        if (!config.enabled || LOG_LEVELS.ERROR < config.minLevel) return
        const offsetStr =
          offset !== undefined && config.showOffsets
            ? `[0x${offset.toString(16).padStart(4, '0')}] `
            : ''
        if (error) {
          console.error(`[${prefix}] ${offsetStr}${message}`, error)
        } else {
          console.error(`[${prefix}] ${offsetStr}${message}`)
        }
      },
    }
  },
}
