{
  "extends": "../../tsconfig.json",
  "compilerOptions": {
    "baseUrl": ".",
    "allowJs": true,
    "noEmit": true,
    "disableSourceOfProjectReferenceRedirect": true,
    "disableSolutionSearching": true,
    "disableReferencedProjectLoad": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@payload-config": [
        "./src/payload.config.ts"
      ],
      "react": [
        "./node_modules/@types/react"
      ],
      "@/*": [
        "./src/*"
      ],
    }
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.js",
    "src/**/*.d.ts",
    ".next/types/**/*.ts",
    "redirects.js",
    "next.config.js",
    "next-sitemap.config.cjs",
    "src/app/(payload)/admin/importMap.js",
    "src/cssVariables.js"
  ],
  "exclude": [
    "node_modules",
    "thirdparty"
  ],
  "references": [
    { "path": "../embroidery-processor" }
  ]
}
