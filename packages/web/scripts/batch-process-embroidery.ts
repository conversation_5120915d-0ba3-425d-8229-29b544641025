#!/usr/bin/env node

/**
 * Batch Embroidery File Processor
 *
 * Reads embroidery files from an input directory, parses them,
 * generates metadata (JSON per file, CSV summary), and renders
 * a scaled, transparent PNG preview image for each supported file.
 *
 * This script uses the core embroidery file processing module to handle
 * various embroidery file formats and generate consistent output.
 *
 * Features:
 * - Processes multiple embroidery files in batch mode
 * - Generates PNG preview images with configurable settings
 * - Extracts metadata in both JSON (per file) and CSV (summary) formats
 * - Handles errors gracefully, continuing processing even if some files fail
 *
 * Usage:
 * ```
 * npx ts-node scripts/batch-process-embroidery.ts [inputDir] [outputDir]
 * ```
 *
 * Example:
 * ```
 * npx ts-node scripts/batch-process-embroidery.ts ./embroidery-files ./output
 * ```
 *
 * The script will:
 * 1. Find all supported embroidery files in the input directory
 * 2. Process each file to extract pattern data
 * 3. Generate a PNG preview image for each file
 * 4. Save metadata as JSON for each file
 * 5. Create a CSV summary of all processed files
 */

import fs from 'fs'
import path from 'path'
import {
  Pattern,
  processEmbroideryBuffer,
  extractMetadata,
  renderPatternOnContext,
  EmbroideryFileMetadata,
  FILE_FORMAT_MAP,
  GenericCanvasContext,
} from '@stitchestimate/embroidery-processor'
import { createCanvas } from 'canvas'

// --- Configuration Constants ---

// File Paths
const DEFAULT_INPUT_DIR = './embroidery-files'
const DEFAULT_OUTPUT_DIR = './output'
const OUTPUT_IMAGES_SUBDIR = 'images'
const OUTPUT_CSV_FILENAME = 'embroidery_data.csv'

// Rendering Parameters
const TARGET_LONGEST_PX = 2048 // Desired length of the longest side in pixels for the *pattern*
const PIXEL_PADDING = 40 // Padding in pixels around the scaled design in the final image
const USE_3D_EFFECT = true // Render with shadow/highlight effect
const LINE_WIDTH_PX = 2.5 // Target line width in the final *output* pixels
const MAX_CANVAS_DIM = 16384 // Safety limit for canvas dimensions

// Metadata
const METADATA_UNITS = 'mm'

// --- Type Definitions ---

// Define a type for the specific metadata structure produced by this script.
// This extends the base EmbroideryFileMetadata to ensure width/height are objects with units
type ProcessedMetadata = Omit<EmbroideryFileMetadata, 'width' | 'height'> & {
  width: { value: number; unit: string }
  height: { value: number; unit: string }
}

// Define explicit return types for the file processing function
type ReadSuccessResult = {
  status: 'success'
  metadata: ProcessedMetadata
  pattern: Pattern
  imagePath: string
}

type ReadMetadataOnlyResult = {
  status: 'metadata_only'
  metadata: ProcessedMetadata
  pattern: Pattern // Include pattern even if image failed/skipped
  reason: string // Reason for not generating image
}

type ReadErrorResult = {
  status: 'error'
  error: Error
  filePath: string
}

type ReadFileResult = ReadSuccessResult | ReadMetadataOnlyResult | ReadErrorResult

// --- Utility Functions ---

/** Checks if a filename has a supported and known extension. */
function isSupportedFormat(filename: string): boolean {
  return path.extname(filename).toLowerCase() in FILE_FORMAT_MAP
}

/** Recursively finds all files in a directory. */
function findAllFiles(dir: string): string[] {
  let results: string[] = []
  try {
    const list = fs.readdirSync(dir)
    list.forEach((file) => {
      const filePath = path.join(dir, file)
      const stat = fs.statSync(filePath)
      if (stat && stat.isDirectory()) {
        results = results.concat(findAllFiles(filePath))
      } else {
        results.push(filePath)
      }
    })
  } catch (err) {
    console.error(`❌ Error reading directory ${dir}:`, err)
  }
  return results
}

// --- Core Processing Logic ---

/**
 * Reads, parses, and optionally renders a single embroidery file.
 * Returns a result object indicating success, partial success (metadata only), or error.
 */
async function processEmbroideryFile(filePath: string, imagesDir: string): Promise<ReadFileResult> {
  const filename = path.basename(filePath)
  const extension = path.extname(filename).toLowerCase()
  let pattern: Pattern | null = null // Keep pattern accessible

  try {
    // 1. Read File Buffer
    let fileBuffer: Buffer
    try {
      fileBuffer = fs.readFileSync(filePath)
    } catch (readError: any) {
      throw new Error(`Failed to read file: ${readError.message || readError}`)
    }
    const arrayBuffer = fileBuffer.buffer.slice(
      fileBuffer.byteOffset,
      fileBuffer.byteOffset + fileBuffer.byteLength,
    )

    // 2. Process the buffer using our core function
    try {
      pattern = await processEmbroideryBuffer(arrayBuffer, filename)
    } catch (parseError: any) {
      // Re-throw parse errors to be caught by the main catch block
      throw new Error(`Error parsing file contents: ${parseError.message || parseError}`)
    }

    // 3. Calculate Dimensions & Handle Empty
    pattern.calculateBoundingBox()
    const originalWidth = pattern.calculateWidth()
    const originalHeight = pattern.calculateHeight()

    // 4. Extract metadata
    const metadataBase = extractMetadata(pattern, filename, METADATA_UNITS) as ProcessedMetadata

    if (pattern.stitches.length === 0 || originalWidth <= 0 || originalHeight <= 0) {
      console.warn(`  ⚠️ Pattern is empty or has zero dimensions. Skipping image generation.`)
      return {
        status: 'metadata_only',
        metadata: metadataBase,
        pattern,
        reason: 'Empty or zero dimension pattern',
      }
    }

    // 5. Prepare for Rendering
    pattern.moveToPositive() // Normalize coordinates for rendering
    const patternWidthMM = pattern.right
    const patternHeightMM = pattern.bottom
    const longestDimMM = Math.max(patternWidthMM, patternHeightMM)

    if (longestDimMM <= 0) {
      // Should not happen if previous check passed, but good sanity check
      console.warn(
        `  ⚠️ Calculated longest dimension is zero or negative after normalization. Skipping image generation.`,
      )
      return {
        status: 'metadata_only',
        metadata: metadataBase,
        pattern,
        reason: 'Invalid dimensions after normalization',
      }
    }

    const dynamicScale = TARGET_LONGEST_PX / longestDimMM // pixels per mm
    const scaledPatternWidthPX = patternWidthMM * dynamicScale
    const scaledPatternHeightPX = patternHeightMM * dynamicScale
    const finalCanvasWidth = Math.ceil(scaledPatternWidthPX + PIXEL_PADDING * 2)
    const finalCanvasHeight = Math.ceil(scaledPatternHeightPX + PIXEL_PADDING * 2)

    // Check canvas size limits
    if (
      finalCanvasWidth <= 0 ||
      finalCanvasHeight <= 0 ||
      finalCanvasWidth > MAX_CANVAS_DIM ||
      finalCanvasHeight > MAX_CANVAS_DIM
    ) {
      console.error(
        `  ❌ Calculated canvas dimensions too large or invalid (${finalCanvasWidth}x${finalCanvasHeight}). Skipping image generation.`,
      )
      return {
        status: 'metadata_only',
        metadata: metadataBase,
        pattern,
        reason: 'Calculated canvas dimensions exceed limits',
      }
    }

    // 6. Render Image
    let imagePath = ''
    try {
      const canvas = createCanvas(finalCanvasWidth, finalCanvasHeight)
      const ctx = canvas.getContext('2d')

      // Prepare context (padding and scaling)
      ctx.save()
      ctx.translate(PIXEL_PADDING, PIXEL_PADDING)
      ctx.scale(dynamicScale, dynamicScale)

      // Draw stitches using our shared rendering function
      renderPatternOnContext(
        ctx as unknown as GenericCanvasContext,
        pattern,
        dynamicScale,
        LINE_WIDTH_PX,
        USE_3D_EFFECT,
      )

      ctx.restore() // Restore context before saving

      // Save PNG
      const pngBuffer = canvas.toBuffer('image/png')
      const baseFilename = path.basename(filename, extension)
      imagePath = path.join(imagesDir, `${baseFilename}.png`)

      try {
        fs.writeFileSync(imagePath, pngBuffer)
        console.log(`  ✓ Image saved to ${imagePath} (${finalCanvasWidth}x${finalCanvasHeight})`)
        // Image successfully written
        return { status: 'success', metadata: metadataBase, pattern, imagePath }
      } catch (writeError: any) {
        console.error(
          `  ❌ Error saving PNG image for ${filename}: ${writeError.message || writeError}`,
        )
        // Image write failed, return metadata only
        return {
          status: 'metadata_only',
          metadata: metadataBase,
          pattern,
          reason: `Failed to write image file: ${writeError.message || writeError}`,
        }
      }
    } catch (renderError: any) {
      console.error(
        `  ❌ Error during image rendering for ${filename}: ${renderError.message || renderError}`,
      )
      // Rendering failed, return metadata only
      return {
        status: 'metadata_only',
        metadata: metadataBase,
        pattern,
        reason: `Image rendering failed: ${renderError.message || renderError}`,
      }
    }
  } catch (error: any) {
    // Catch all other errors (read, parse, unsupported format etc.)
    console.error(`❌ Processing failed entirely for ${filename}: ${error.message || error}`)
    return { status: 'error', error: error, filePath: filePath }
  }
}

/** Processes all embroidery files found in the input directory. */
async function processBatch(
  inputDir: string,
  outputDir: string,
  imagesDir: string,
): Promise<ProcessedMetadata[]> {
  console.log(`\nStarting processing...\n`)
  const allFoundFiles = findAllFiles(inputDir)
  const filesToProcess = allFoundFiles.filter(isSupportedFormat)

  console.log(
    `Found ${allFoundFiles.length} total files, ${filesToProcess.length} supported embroidery files.`,
  )

  const processedMetadataList: ProcessedMetadata[] = []
  let successCount = 0 // Files with successful image generation AND metadata save
  let metadataOnlyCount = 0 // Files where only metadata was saved
  let errorCount = 0 // Files that failed entirely

  for (let i = 0; i < filesToProcess.length; i++) {
    const filePath = filesToProcess[i]
    const fileIndex = i + 1
    const totalFiles = filesToProcess.length
    const filename = path.basename(filePath)

    console.log(`Processing [${fileIndex}/${totalFiles}]: ${filename}`)

    const result = await processEmbroideryFile(filePath, imagesDir)

    switch (result.status) {
      case 'success':
        processedMetadataList.push(result.metadata)
        // Attempt to save JSON metadata
        const metaSuccessPath = path.join(outputDir, `${path.basename(filePath)}.json`)
        try {
          fs.writeFileSync(metaSuccessPath, JSON.stringify(result.metadata, null, 2))
          console.log(`  ✓ Metadata saved to ${metaSuccessPath}`)
          successCount++ // Only count success if both image and metadata saved
        } catch (metaWriteError: any) {
          console.error(
            `  ❌ Error saving metadata for ${filename} after successful image write: ${metaWriteError.message || metaWriteError}`,
          )
          errorCount++ // Treat metadata write failure as an overall error for this file
        }
        break

      case 'metadata_only':
        processedMetadataList.push(result.metadata)
        metadataOnlyCount++
        // Attempt to save JSON metadata even if image failed/skipped
        const metaOnlyPath = path.join(outputDir, `${path.basename(filePath)}.json`)
        try {
          fs.writeFileSync(metaOnlyPath, JSON.stringify(result.metadata, null, 2))
          console.log(
            `  ✓ Metadata saved to ${metaOnlyPath} (image skipped/failed: ${result.reason})`,
          )
        } catch (metaWriteError: any) {
          console.error(
            `  ❌ Error saving metadata for ${filename} after image skip/failure: ${metaWriteError.message || metaWriteError}`,
          )
          errorCount++ // Count as error if metadata couldn't be saved
        }
        break

      case 'error':
        errorCount++
        // Log already happened in processEmbroideryFile
        break
    }
    console.log(`  --- Finished [${fileIndex}/${totalFiles}] ---`)
  }

  // --- Generate CSV Summary ---
  if (processedMetadataList.length > 0) {
    const csvPath = path.join(outputDir, OUTPUT_CSV_FILENAME)
    const csvHeader = 'filename,format,width_mm,height_mm,stitchCount,colorCount\n'
    let csvContent = csvHeader
    for (const metadata of processedMetadataList) {
      const widthVal = metadata.width?.value?.toFixed(2) ?? 'N/A'
      const heightVal = metadata.height?.value?.toFixed(2) ?? 'N/A'
      csvContent += `${metadata.filename || 'N/A'},${metadata.format || 'N/A'},${widthVal},${heightVal},${metadata.stitchCount ?? 'N/A'},${metadata.colorCount ?? 'N/A'}\n`
    }
    try {
      fs.writeFileSync(csvPath, csvContent)
      console.log(`\n✓ CSV data saved to: ${csvPath}`)
    } catch (csvError: any) {
      console.error(`\n❌ Error saving CSV data: ${csvError.message || csvError}`)
    }
  } else {
    console.log(`\n⚠️ No metadata generated, CSV not created.`)
  }

  // --- Final Summary ---
  console.log(`\nProcessing complete!`)
  console.log(`  Successfully generated images & metadata: ${successCount}`)
  console.log(`  Generated metadata only (image skipped/failed): ${metadataOnlyCount}`)
  console.log(`  Failed entirely: ${errorCount}`)
  console.log(`\nOutput locations:`)
  console.log(`  PNG images: ${imagesDir}`)
  console.log(`  Metadata JSON files: ${outputDir}`)
  console.log(`  CSV summary: ${path.join(outputDir, OUTPUT_CSV_FILENAME)}`)

  return processedMetadataList
}

// --- Main Execution ---

async function main() {
  const inputDir = process.argv[2] || DEFAULT_INPUT_DIR
  const outputDir = process.argv[3] || DEFAULT_OUTPUT_DIR

  // Ensure output directories exist
  const imagesDir = path.join(outputDir, OUTPUT_IMAGES_SUBDIR)
  try {
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }
    if (!fs.existsSync(imagesDir)) {
      fs.mkdirSync(imagesDir, { recursive: true })
    }
  } catch (dirError: any) {
    console.error(`❌ Fatal error creating output directories: ${dirError.message || dirError}`)
    process.exit(1)
  }

  // Print configuration
  console.log(`
Batch Embroidery File Processor
==============================
Input folder: ${inputDir}
Output folder: ${outputDir}
Target Longest Dimension: ${TARGET_LONGEST_PX}px (approx)
Padding: ${PIXEL_PADDING}px
3D Effect: ${USE_3D_EFFECT}
Background: Transparent
Supported formats: ${Object.keys(FILE_FORMAT_MAP)
    .filter((k) => k !== '.xxx' && k !== '.sew' && k !== '.pcs')
    .join(', ')}
==============================
`)

  await processBatch(inputDir, outputDir, imagesDir)
}

main().catch((error) => {
  console.error('❌ Unhandled error in main process:', error)
  process.exit(1)
})
