NODE_ENV=test

# Added by Payload
# Database connection string
DATABASE_URI=postgres://postgres:postgres@127.0.0.1:5433/stitchestimate
# Used to encrypt JWT tokens
PAYLOAD_SECRET=test-secret
# Used to configure CORS, format links and more. No trailing slash
NEXT_PUBLIC_SERVER_URL=http://localhost:4000


# Paddle
## Private
NEXT_PUBLIC_PADDLE_ENV=sandbox # or `production`
PADDLE_API_KEY=api-key-sandbox
PADDLE_NOTIFICATION_WEBHOOK_SECRET=webhook-secret-sandbox
NEXT_PUBLIC_PADDLE_CLIENT_TOKEN=public-key-sandbox

REACT_EDITOR=atom

# Default Test User
DEFAULT_TEST_EMAIL=<EMAIL>
DEFAULT_TEST_PASSWORD=admin 

# Object Storage (for PDF reports, media uploads, and static assets)
## Required for production
OBJECT_STORAGE_ACCESS_KEY=88a19ef16f9541488ae291fc3e6af689
OBJECT_STORAGE_SECRET_KEY=be6bd2a530dd430b96f0e4321dff29bade6f865545207b228d181483071dc704
OBJECT_STORAGE_BUCKET=stitchestimate-dev
## Optional - defaults are suitable for most S3-compatible services
OBJECT_STORAGE_REGION=auto
OBJECT_STORAGE_ENDPOINT=https://1f1bed5af0d4453a35a000049a77928b.r2.cloudflarestorage.com
OBJECT_STORAGE_PATH_STYLE=true