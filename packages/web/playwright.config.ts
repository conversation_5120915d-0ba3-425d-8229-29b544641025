import { defineConfig, devices } from '@playwright/test'
import dotenv from 'dotenv'
import path from 'path'
import { fileURLToPath } from 'url'

// Get the directory name in ES modules
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Read from ".env.test" file in the web package directory
dotenv.config({ path: path.resolve(__dirname, '.env.test') })

export default defineConfig({
  testDir: './e2e',
  /* Timeout per test */
  timeout: 60 * 1000, // 60 seconds
  workers: 1,
  reporter: 'list',
  use: {
    baseURL: 'http://localhost:4000',
    screenshot: 'only-on-failure',
  },
  outputDir: './test-results',
  webServer: {
    command: 'echo "HA!" && pnpm dev:test',
    url: 'http://localhost:4000',
    reuseExistingServer: true,
    cwd: __dirname, // Ensure we're using the web package directory
    env: {
      NODE_ENV: 'test',
    },
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
  ],
})
