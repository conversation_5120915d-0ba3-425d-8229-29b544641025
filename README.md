# StitchEstimate

StitchEstimate is a professional web application designed to help embroidery businesses calculate accurate stitch counts and production times for their designs. Built with modern web technologies, it provides a user-friendly interface for quick and precise embroidery estimates.

## Core Features

- **Accurate Stitch Count Calculation**: Upload your design and get precise stitch count estimates
- **Production Time Estimation**: Automatically calculate production time based on stitch count
- **Background Removal**: Intelligent background detection and removal for cleaner designs
- **Garment-Specific Calculations**: Different stitch density calculations based on garment type
- **Real-time Size Adjustment**: Interactive canvas for adjusting design dimensions
- **Units Conversion**: Support for both metric (mm) and imperial (inches) measurements
- **Professional Reporting**: Clear presentation of estimates for client communication

## Technology Stack

- Next.js 14 (App Router)
- TypeScript
- TailwindCSS
- Shadcn/UI Components
- Sharp for Image Processing
- Playwright for E2E Testing
- Paddle for Payments and Subscriptions

## Getting Started

### Prerequisites

- Node.js 18+
- pnpm
- Docker (for development database)

### Development Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/LambdaCoffieLLC/StitchEstimate.git
   cd stitchestimate
   ```

2. Install dependencies:
   ```bash
   pnpm install
   ```

3. Copy the environment variables:
   ```bash
   cp .env.example .env
   ```

4. Start the development server:
   ```bash
   pnpm dev
   ```

The application will be available at `http://localhost:3000`.

### Managing Garment Types

Garment types are managed through Payload CMS and backed up to a JSON file for version control and development purposes.

#### Structure
Each garment type has the following properties:
- `name`: Display name of the garment
- `slug`: URL-friendly identifier
- `stitchDensity`: Number of stitches per square millimeter
- `svgTemplate`: SVG markup for the garment template
- `dimensions`: Default dimensions (width, height) in inches or millimeters
- `active`: Whether the garment type is available for selection

#### Managing via Admin UI

1. **Accessing the Admin Panel**
   - Navigate to `/admin` in your browser
   - Log in with your admin credentials
   - Go to the "Garment Types" collection

2. **Making Changes**
   - Add, edit, or deactivate garment types using the admin interface
   - All changes are immediately saved to the database

3. **Exporting to JSON**
   - Click the "Export Garment Types" button at the top of the list
   - This will download a `garment-types.json` file
   - Commit this file to version control to track changes

4. **Importing from JSON**
   - Click the "Import Garment Types" button at the top of the list
   - Select a previously exported `garment-types.json` file
   - The system will create or update garment types based on the file
   - Existing garment types are matched by their slug

#### Command Line Tools

You can also manage garment types via command line. The scripts are located in the `scripts/garment-types` directory:

1. **Export to JSON**
   ```bash
   pnpm export:garment-types
   ```
   This will update `src/data/garment-types.json`

2. **Import from JSON**
   ```bash
   pnpm import:garment-types
   ```
   This will populate the database from `src/data/garment-types.json`

#### Development Workflow

1. Make changes in the admin panel
2. Export to JSON using either the UI button or command line
3. Commit the JSON file to version control
4. When setting up new environments, import the JSON file using either the UI button or command line

This ensures that:
- All environments have consistent garment types
- Changes are tracked in version control
- The JSON file serves as the source of truth
- New environments can be quickly populated with the correct data

### Admin Access

#### Resetting Admin Credentials
If you need to reset or create the admin user credentials, run:
```bash
pnpm reset:admin
```

This will:
- Create a new admin user if none exists
- Reset the password if an admin user already exists
- Set the credentials to:
  - Email: <EMAIL>
  - Password: admin

**Important**: Make sure to change these credentials immediately after logging in for the first time.

The admin reset script is located in the `scripts` directory.

### Testing

For detailed information about running tests, debugging, and CI/CD integration, please refer to our [Testing Documentation](TESTING.md).

## Pricing Plans

StitchEstimate offers flexible pricing options powered by Paddle for secure payment processing and subscription management:

### Unlimited Plan
- Unlimited estimates
- All estimation features
- Monthly or annual billing (20% discount)
- Perfect for regular users
- Automatic billing and subscription management
- Secure payment processing with Paddle

### Pay As You Go
- Credit-based system
- No subscription required
- Credits never expire
- Ideal for occasional users
- One-time secure purchases through Paddle
- Instant credit delivery

## License

This project is proprietary software. All rights reserved.

## Support

For support inquiries, <NAME_EMAIL> or visit our documentation at https://docs.stitchestimate.com
