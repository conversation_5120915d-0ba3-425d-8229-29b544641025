/**
 * Logger utility for embroidery processor
 */

// Create a simple logger for the embroidery processor
export const logger = {
  debug: (message: string, ...args: any[]) => {
    console.debug(`[EmbroideryProcessor] ${message}`, ...args)
  },
  log: (message: string, ...args: any[]) => {
    console.log(`[EmbroideryProcessor] ${message}`, ...args)
  },
  warn: (message: string, ...args: any[]) => {
    console.warn(`[EmbroideryProcessor] ${message}`, ...args)
  },
  error: (message: string, error?: unknown) => {
    if (error) {
      console.error(`[EmbroideryProcessor] ${message}`, error)
    } else {
      console.error(`[EmbroideryProcessor] ${message}`)
    }
  },
  createFormatLogger: (formatName: string) => {
    const prefix = `EmbroideryProcessor:${formatName}`
    return {
      debug: (message: string, offset?: number, ...args: any[]) => {
        const offsetStr = offset !== undefined ? `[0x${offset.toString(16).padStart(4, '0')}] ` : ''
        console.debug(`[${prefix}] ${offsetStr}${message}`, ...args)
      },
      log: (message: string, offset?: number, ...args: any[]) => {
        const offsetStr = offset !== undefined ? `[0x${offset.toString(16).padStart(4, '0')}] ` : ''
        console.log(`[${prefix}] ${offsetStr}${message}`, ...args)
      },
      warn: (message: string, offset?: number, ...args: any[]) => {
        const offsetStr = offset !== undefined ? `[0x${offset.toString(16).padStart(4, '0')}] ` : ''
        console.warn(`[${prefix}] ${offsetStr}${message}`, ...args)
      },
      error: (message: string, error?: unknown, offset?: number) => {
        const offsetStr = offset !== undefined ? `[0x${offset.toString(16).padStart(4, '0')}] ` : ''
        if (error) {
          console.error(`[${prefix}] ${offsetStr}${message}`, error)
        } else {
          console.error(`[${prefix}] ${offsetStr}${message}`)
        }
      },
    }
  },
}
